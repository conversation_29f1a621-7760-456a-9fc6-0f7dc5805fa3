"""
回测CandlestickVQGPT模型

该脚本用于对训练好的CandlestickVQGPT模型进行回测，评估其在实际交易中的表现。
支持加载ONNX格式的模型和码本。
"""

import os
import sys
import argparse
import json
import time
import logging
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和回测器
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.backtester import CandlestickLLMBacktester
from pyqlab.models.gpt2.utils import load_single_data, extract_time_features

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("backtest_candlestick_vq_gpt.log")
    ]
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='回测CandlestickVQGPT模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本文件路径')
    parser.add_argument('--vectorization_method', type=str, default='percent_change',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    parser.add_argument('--use_code_dim', action='store_true', help='是否使用证券代码维度')
    parser.add_argument('--code_dim', type=int, default=5, help='证券代码嵌入维度')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')

    # 回测参数
    parser.add_argument('--initial_capital', type=float, default=10000.0, help='初始资金')
    parser.add_argument('--commission', type=float, default=0.001, help='交易手续费率')
    parser.add_argument('--threshold', type=float, default=0.6, help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float, default=None, help='止损比例')
    parser.add_argument('--take_profit', type=float, default=None, help='止盈比例')
    parser.add_argument('--temperature', type=float, default=1.5, help='温度参数')
    parser.add_argument('--top_k', type=int, default=50, help='Top-K采样参数')
    parser.add_argument('--top_p', type=float, default=0.9, help='Nucleus采样参数')
    parser.add_argument('--signal_type', type=str, default='topk',
                        choices=['threshold', 'topk', 'momentum', 'ensemble'],
                        help='信号生成器类型')
    parser.add_argument('--leverage', type=float, default=1.0, help='杠杆倍数')
    parser.add_argument('--print_interval', type=int, default=10, help='打印间隔')

    # 其他参数
    parser.add_argument('--output_dir', type=str, default='./results', help='输出目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def create_tokenizer(args):
    """创建Tokenizer"""
    logger.info("创建Tokenizer...")

    # 选择向量化方法
    if args.vectorization_method == 'atr_based':
        vectorization_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vectorization_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vectorization_method = VectorizationMethod.LOG_RETURN
    elif args.vectorization_method == 'zscore':
        vectorization_method = VectorizationMethod.ZSCORE
    elif args.vectorization_method == 'minmax':
        vectorization_method = VectorizationMethod.MINMAX
    else:
        raise ValueError(f"未知的向量化方法: {args.vectorization_method}")

    # 从配置文件中获取参数
    config = {}
    if args.model_path:
        config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            logger.warning(f"未找到模型配置文件: {config_path}")
    else:
        logger.info("未指定模型路径，使用默认配置")

    # 创建Tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=config.get('num_embeddings', 512),
        embedding_dim=config.get('embedding_dim', 4),
        atr_period=config.get('atr_period', 14),
        ma_volume_period=config.get('ma_volume_period', 14),
        vectorization_method=vectorization_method,
        detect_gaps=True,
        gap_threshold=7.0,
        use_code_dim=args.use_code_dim,
        code_dim=args.code_dim
    )

    logger.info(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")

    return tokenizer


def load_model(args, tokenizer):
    """加载模型"""
    logger.info(f"加载模型: {args.model_path}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 检查是否是ONNX模型
    if args.model_path.endswith('.onnx'):
        # 使用ONNX运行时加载模型
        try:
            import onnxruntime as ort
            logger.info("使用ONNX运行时加载模型")
            # 创建一个包装类来模拟PyTorch模型接口
            class ONNXModelWrapper:
                def __init__(self, onnx_path, device):
                    self.onnx_path = onnx_path
                    self.device = device
                    # 创建ONNX运行时会话
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if device.type == 'cuda' else ['CPUExecutionProvider']
                    self.session = ort.InferenceSession(onnx_path, providers=providers)
                    # 获取输入和输出名称
                    self.input_names = [input.name for input in self.session.get_inputs()]
                    self.output_names = [output.name for output in self.session.get_outputs()]

                def to(self, device):
                    # 不需要实际操作，只是为了兼容PyTorch接口
                    return self

                def eval(self):
                    # 不需要实际操作，只是为了兼容PyTorch接口
                    return self

                def __call__(self, input_tokens, code_ids, time_features=None):
                    """
                    模拟PyTorch模型的前向传播

                    Args:
                        input_tokens: 输入token序列，形状为(batch_size, seq_len)
                        code_ids: 证券代码ID，形状为(batch_size)
                        time_features: 时间特征，形状为(batch_size, seq_len, n_time_features)

                    Returns:
                        logits: 模型输出
                        loss: 损失值（始终为None，因为ONNX模型不计算损失）
                    """
                    # 准备输入
                    inputs = {
                        'input_tokens': input_tokens.cpu().numpy(),
                        'code_ids': code_ids.cpu().numpy()
                    }

                    if time_features is not None and 'time_features' in self.input_names:
                        inputs['time_features'] = time_features.cpu().numpy()

                    # 运行推理
                    outputs = self.session.run(self.output_names, inputs)

                    # 将输出转换为PyTorch张量
                    logits = torch.tensor(outputs[0], device=self.device)

                    return logits, None

                def generate(self, input_tokens, code_ids, time_features=None, max_new_tokens=10, temperature=1.0, top_k=None):
                    # 准备输入
                    inputs = {
                        'input_tokens': input_tokens.cpu().numpy(),
                        'code_ids': code_ids.cpu().numpy()
                    }
                    if time_features is not None and 'time_features' in self.input_names:
                        inputs['time_features'] = time_features.cpu().numpy()

                    # 运行推理
                    outputs = self.session.run(self.output_names, inputs)

                    # 将输出转换为PyTorch张量
                    logits = torch.tensor(outputs[0], device=self.device)

                    # 生成新的token
                    batch_size, seq_len = input_tokens.size()
                    tokens = input_tokens.clone()

                    for _ in range(max_new_tokens):
                        # 获取最后一个时间步的logits
                        last_logits = logits[:, -1, :] / (temperature if temperature > 0 else 1.0)

                        # 可选的top-k采样
                        if top_k is not None:
                            v, _ = torch.topk(last_logits, min(top_k, last_logits.size(-1)))
                            last_logits[last_logits < v[:, [-1]]] = -float('Inf')

                        # 应用softmax获取概率分布
                        probs = torch.nn.functional.softmax(last_logits, dim=-1)

                        # 采样下一个token
                        next_token = torch.multinomial(probs, num_samples=1)

                        # 将新token添加到序列中
                        tokens = torch.cat([tokens, next_token], dim=1)

                        # 准备下一次推理的输入
                        inputs = {
                            'input_tokens': tokens.cpu().numpy(),
                            'code_ids': code_ids.cpu().numpy()
                        }
                        if time_features is not None and 'time_features' in self.input_names:
                            # 扩展时间特征
                            new_time_features = torch.zeros(batch_size, 1, time_features.size(2), device=time_features.device)
                            time_features = torch.cat([time_features, new_time_features], dim=1)
                            inputs['time_features'] = time_features.cpu().numpy()

                        # 运行推理
                        outputs = self.session.run(self.output_names, inputs)
                        logits = torch.tensor(outputs[0], device=self.device)

                    return tokens

            # 创建ONNX模型包装器
            model = ONNXModelWrapper(args.model_path, device)

        except ImportError:
            logger.error("未安装onnxruntime，无法加载ONNX模型")
            raise
    else:
        # 加载PyTorch模型
        logger.info("加载PyTorch模型")

        # 从检查点中获取模型配置
        config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            config = {}

        # 加载检查点
        checkpoint = torch.load(args.model_path, map_location=device)
        config = checkpoint.get('config', {})

        # 使用tokenizer的词汇表大小，而不是配置中的默认值
        vocab_size = tokenizer.vocab_size

        # 创建模型，使用正确的词汇表大小
        model = CandlestickVQGPT(
            vocab_size=vocab_size,
            code_size=config.get('code_size', 100),
            seq_len=config.get('seq_len', config.get('block_size', args.seq_len)),
            n_layer=config.get('n_layer', 4),
            n_head=config.get('n_head', 8),
            d_model=config.get('d_model', 128),
            dropout=0.0,  # 推理时不需要dropout
            bias=False,
            use_time_features=config.get('use_time_features', args.use_time_features),
            n_time_features=config.get('n_time_features', 8),
            label_smoothing=0.0,  # 推理时不需要label smoothing
            use_auxiliary_loss=False  # 推理时不需要辅助损失
        )

        # 加载模型权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        model.to(device)

        logger.info(f"模型加载成功，参数数量: {model.get_num_params():,}")
        logger.info(f"模型词汇表大小: {model.vocab_size}")
        logger.info(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")

    logger.info(f"使用设备: {device}")

    return model, device


def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载数据
    logger.info(f"加载数据: {args.data_path}")
    data, code_ids = load_single_data(
        args.data_path,
        begin_date=args.begin_date,
        end_date=args.end_date
    )

    if len(data) == 0:
        logger.error("未找到符合条件的数据")
        return

    # 选择第一个证券进行回测
    df = data[1]
    code_id = code_ids[1]
    logger.info(f"使用证券代码 {code_id} 进行回测")
    logger.info(f"数据形状: {df.shape}")

    # 创建tokenizer
    tokenizer = create_tokenizer(args)

    # 加载模型
    model, device = load_model(args, tokenizer)

    # 创建回测器
    logger.info("创建回测器...")
    backtester = CandlestickLLMBacktester(
        model=model,
        tokenizer=tokenizer,
        initial_capital=args.initial_capital,
        device=device,
        signal_type=args.signal_type,
        leverage=args.leverage
    )

    # 进行回测
    logger.info("\n开始回测...")
    results = backtester.backtest(
        df=df,
        code_id=code_id,
        seq_len=args.seq_len,
        commission=args.commission,
        threshold=args.threshold,
        stop_loss=args.stop_loss,
        take_profit=args.take_profit,
        time_features=extract_time_features(df) if args.use_time_features else None,
        temperature=args.temperature,
        top_k=args.top_k,
        top_p=args.top_p,
        print_interval=args.print_interval
    )

    # 可视化回测结果
    logger.info("\n可视化回测结果...")
    backtester.visualize_backtest(
        df=df,
        results=results,
        seq_len=args.seq_len,
        save_path=os.path.join(args.output_dir, 'backtest_chart.png')
    )

    # 保存回测结果
    results_path = os.path.join(args.output_dir, 'backtest_results.json')
    backtester.save_results(results, results_path)

    logger.info(f"\n回测完成，结果已保存到 {args.output_dir}")


if __name__ == "__main__":
    main()
