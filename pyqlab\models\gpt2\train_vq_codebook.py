"""
训练CandlestickVQTokenizer的码本权重

这个脚本用于训练VQ-VAE模型，并保存其码本权重，以便在CandlestickVQTokenizer中使用。
"""

import os
import json
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from tqdm import tqdm
import glob
from datetime import datetime
import talib
from pyqlab.const import MODEL_FUT_CODES

from pyqlab.models.gpt2.utils import get_data_files, load_single_data, get_code_id
# 导入CandlestickVQTokenizer中的向量化函数
from pyqlab.models.gpt2.vq_tokenizer import candlestick_to_vector, VQEmbedding
from pyqlab.models.gpt2.code_aware_codebook import CodeAwareEncoder, CodeAwareDecoder

class KLineDataset(Dataset):
    """K线数据集，用于训练VQ-VAE模型"""

    def __init__(self, data_files, num_embeddings=512, atr_period=14,
                 ma_volume_period=20, vector_dim=5, min_samples=10000,
                 max_samples=500000, max_samples_per_code=50000,
                 vectorization_method='atr_based', use_code_dim=False):
        """
        初始化数据集

        Args:
            data_files: K线数据文件列表
            num_embeddings: 码本大小
            atr_period: ATR计算周期
            ma_volume_period: 成交量移动平均周期
            vector_dim: 向量维度
            min_samples: 最小样本数
            max_samples: 最大样本数
            max_samples_per_code: 每个证券代码的最大样本数
            vectorization_method: 向量化方法，默认为'atr_based'
            use_code_dim: 是否使用证券代码维度
        """
        self.data_files = data_files
        self.num_embeddings = num_embeddings
        self.atr_period = atr_period
        self.ma_volume_period = ma_volume_period
        self.vector_dim = vector_dim
        self.min_samples = min_samples
        self.max_samples = max_samples
        self.max_samples_per_code = max_samples_per_code
        self.vectorization_method = vectorization_method
        self.use_code_dim = use_code_dim

        # 加载并预处理数据
        self.vectors, self.code_ids, self.code_to_id = self._load_and_preprocess_data()
        print(f"加载了 {len(self.vectors)} 个K线向量")
        if use_code_dim:
            print(f"共有 {len(self.code_to_id)} 个不同的证券代码")

    def _process_single_df(self, df, all_vectors):
        """处理单个DataFrame"""
        # 确保列名小写
        df.columns = [col.lower() for col in df.columns]

        # 确保必要的列存在
        if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            print(f"警告: DataFrame缺少必要的OHLC列，跳过")
            return

        if self.vector_dim == 5 and 'volume' not in df.columns:
            print(f"警告: DataFrame缺少volume列，跳过")
            return

        # 预处理数据
        processed_df = self._preprocess_df(df)

        # 提取有效向量
        for i in range(max(self.atr_period, self.ma_volume_period), len(processed_df)):
            row = processed_df.iloc[i]

            # 检查是否有足够的历史数据
            if pd.isna(row['atr']) or pd.isna(row['ma_volume']) or pd.isna(row['prev_close']):
                print(f"警告: 缺少必要的数据用于向量化: atr={row['atr']}, ma_volume={row['ma_volume']}, prev_close={row['prev_close']}")
                continue
            if row['open'] / row['prev_close'] > 1.2 or row['open'] / row['prev_close'] < 0.8:
                print(f"警告: 开盘价与前收盘价的比值异常: {row['open'] / row['prev_close']}")
                continue

            # 提取向量
            vector = candlestick_to_vector(
                row,
                row['prev_close'],
                row['ma_volume'],
                row['atr'],
                self.vector_dim,
                self.vectorization_method
            )

            # 检查向量有效性
            if np.any(np.isnan(vector)) or np.any(np.isinf(vector)):
                print(f"警告: 无效的向量: {vector}")
                continue

            if self.vectorization_method == 'atr_based':
                # 对百分比变化进行限制，防止过大的值
                for i in range(len(vector)):
                    if i == 0:  # 只对价格相关的维度进行限制
                        if abs(vector[i]) > 1:  # 限制在20%以内
                            vector[i] = 1 if vector[i] > 0 else -1
            elif self.vectorization_method == 'percent_change':
                # 对百分比变化进行限制，防止过大的值
                for i in range(len(vector)):
                    if i < 4:  # 只对价格相关的维度进行限制
                        if abs(vector[i]) > 2:  # 限制在20%以内
                            vector[i] = 2 if vector[i] > 0 else -2

            # 添加到向量列表
            all_vectors.append(vector)

    def _preprocess_df(self, df):
        """预处理DataFrame，计算ATR, MA_Volume, Prev_Close"""
        df = df.copy()

        # ATR
        # df['h-l'] = df['high'] - df['low']
        # df['h-pc'] = abs(df['high'] - df['close'].shift(1))
        # df['l-pc'] = abs(df['low'] - df['close'].shift(1))
        # df['tr'] = df[['h-l', 'h-pc', 'l-pc']].max(axis=1)
        # df['atr'] = df['tr'].rolling(window=self.atr_period, min_periods=self.atr_period).mean()
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=self.atr_period)

        # MA Volume
        if 'volume' in df.columns:
            # df['ma_volume'] = df['volume'].rolling(window=self.ma_volume_period, min_periods=self.ma_volume_period).mean()
            df['ma_volume'] = talib.SMA(df['volume'], timeperiod=self.ma_volume_period)
        else:
            df['ma_volume'] = np.nan

        # Prev Close
        df['prev_close'] = df['close'].shift(1)

        return df

    def _load_and_preprocess_data(self):
        """加载并预处理所有数据文件，限制每个代码的样本数量"""
        # 使用字典存储每个代码的向量
        code_vectors = {}
        # 创建证券代码到ID的映射
        code_to_id = {}
        # next_code_id = 0

        for file_path in tqdm(self.data_files, desc="加载数据文件"):
            try:
                # 加载K线数据
                print(f"处理数据文件: {file_path}")
                try:
                    # 尝试读取Parquet文件
                    if file_path.endswith('.parquet'):
                        df = pd.read_parquet(file_path)
                        # 如果有code列，按code分组处理
                        if 'code' in df.columns:
                            for code, group_df in df.groupby('code'):
                                # 为每个证券代码分配一个唯一ID
                                if code not in code_to_id:
                                    code_to_id[code] = get_code_id(code)

                                # 初始化该代码的向量列表
                                if code not in code_vectors:
                                    code_vectors[code] = []

                                # 处理该代码的数据
                                group_df = group_df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)
                                if len(group_df) > self.max_samples_per_code + self.atr_period:
                                    group_df = group_df[-(self.atr_period + self.max_samples_per_code):]

                                # 创建临时列表存储该代码的向量
                                temp_vectors = []
                                self._process_single_df(group_df, temp_vectors)

                                # 如果该代码的向量数量超过限制，随机采样
                                # if len(temp_vectors) > self.max_samples_per_code:
                                #     print(f"代码 {code} 的样本数量 {len(temp_vectors)} 超过每个代码的最大限制 {self.max_samples_per_code}，随机采样")
                                #     indices = np.random.choice(len(temp_vectors), self.max_samples_per_code, replace=False)
                                #     temp_vectors = [temp_vectors[i] for i in indices]

                                # 添加到该代码的向量列表
                                code_vectors[code].extend(temp_vectors)
                        else:
                            # 没有code列，直接处理整个DataFrame
                            df = df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)

                            # 使用文件名作为代码
                            code = os.path.basename(file_path).split('.')[0]

                            # 为每个证券代码分配一个唯一ID
                            if code not in code_to_id:
                                code_to_id[code] = get_code_id(code)

                            if code not in code_vectors:
                                code_vectors[code] = []

                            # 创建临时列表存储该文件的向量
                            temp_vectors = []
                            self._process_single_df(df, temp_vectors)

                            # 如果该文件的向量数量超过限制，随机采样
                            if len(temp_vectors) > self.max_samples_per_code:
                                print(f"文件 {code} 的样本数量 {len(temp_vectors)} 超过每个代码的最大限制 {self.max_samples_per_code}，随机采样")
                                indices = np.random.choice(len(temp_vectors), self.max_samples_per_code, replace=False)
                                temp_vectors = [temp_vectors[i] for i in indices]

                            # 添加到该代码的向量列表
                            code_vectors[code].extend(temp_vectors)
                    # 尝试读取CSV文件
                    elif file_path.endswith('.csv'):
                        df = pd.read_csv(file_path)
                        # 确保datetime列存在
                        if 'datetime' not in df.columns and 'date' in df.columns:
                            df.rename(columns={'date': 'datetime'}, inplace=True)

                        df = df.sort_values(by=['datetime'], ascending=True).reset_index(drop=True)

                        # 使用文件名作为代码
                        code = os.path.basename(file_path).split('.')[0]

                        # 为每个证券代码分配一个唯一ID
                        if code not in code_to_id:
                            code_to_id[code] = get_code_id(code)

                        if code not in code_vectors:
                            code_vectors[code] = []

                        # 创建临时列表存储该文件的向量
                        temp_vectors = []
                        self._process_single_df(df, temp_vectors)

                        # 如果该文件的向量数量超过限制，随机采样
                        if len(temp_vectors) > self.max_samples_per_code:
                            print(f"文件 {code} 的样本数量 {len(temp_vectors)} 超过每个代码的最大限制 {self.max_samples_per_code}，随机采样")
                            indices = np.random.choice(len(temp_vectors), self.max_samples_per_code, replace=False)
                            temp_vectors = [temp_vectors[i] for i in indices]

                        # 添加到该代码的向量列表
                        code_vectors[code].extend(temp_vectors)
                except Exception as e:
                    print(f"处理文件 {file_path} 时出错: {e}")
            except Exception as e:
                print(f"加载文件 {file_path} 时出错: {e}")

        # 合并所有代码的向量和对应的代码ID
        all_vectors = []
        all_code_ids = []

        for code, vectors in code_vectors.items():
            # TEST: 仅使用RB9999.SC的代码
            # if code != "RB9999.SC":
            #     continue
            print(f"代码 {code} (ID: {code_to_id[code]}) 的样本数量: {len(vectors)}")
            print(pd.DataFrame(vectors).describe())

            # 使用所有代码的数据
            all_vectors.extend(vectors)
            # 为每个向量添加对应的代码ID
            all_code_ids.extend([code_to_id[code]] * len(vectors))

        print(f"总样本数量: {len(all_vectors)}")
        print(f"总代码数量: {len(code_to_id)}")

        # 检查是否有足够的样本
        if len(all_vectors) == 0:
            print("错误: 没有有效的样本数据，请检查数据文件和过滤条件")
            # 创建一些随机样本用于测试
            print("创建随机样本用于测试...")
            num_random_samples = max(self.min_samples, 1000)
            all_vectors = [np.random.randn(self.vector_dim).astype(np.float32) for _ in range(num_random_samples)]
            # 为随机样本分配随机代码ID
            all_code_ids = [np.random.randint(0, max(1, len(code_to_id))) for _ in range(num_random_samples)]
            print(f"创建了 {len(all_vectors)} 个随机样本")

        # 打印样本统计信息
        df = pd.DataFrame(all_vectors)
        print(df.describe())
        del df

        # 限制总样本数量
        if len(all_vectors) > self.max_samples:
            print(f"总样本数量 {len(all_vectors)} 超过最大限制 {self.max_samples}，随机采样")
            indices = np.random.choice(len(all_vectors), self.max_samples, replace=False)
            all_vectors = [all_vectors[i] for i in indices]
            all_code_ids = [all_code_ids[i] for i in indices]

        if len(all_vectors) < self.min_samples:
            print(f"警告: 总样本数量 {len(all_vectors)} 少于最小要求 {self.min_samples}")
            # 如果样本数量不足，通过复制现有样本来增加样本数量
            if len(all_vectors) > 0:
                print(f"通过复制现有样本增加样本数量到 {self.min_samples}")
                num_copies = self.min_samples // len(all_vectors) + 1
                all_vectors = all_vectors * num_copies
                all_code_ids = all_code_ids * num_copies
                all_vectors = all_vectors[:self.min_samples]
                all_code_ids = all_code_ids[:self.min_samples]
                print(f"增加后的样本数量: {len(all_vectors)}")

        # 先转换为NumPy数组，再转换为PyTorch张量，避免性能警告
        all_vectors_np = np.array(all_vectors, dtype=np.float32)
        all_code_ids_np = np.array(all_code_ids, dtype=np.int64)

        return torch.from_numpy(all_vectors_np), torch.from_numpy(all_code_ids_np), code_to_id

    def __len__(self):
        return len(self.vectors)

    def __getitem__(self, idx):
        if self.use_code_dim:
            return self.vectors[idx], self.code_ids[idx]
        else:
            return self.vectors[idx]


# 定义VQ-VAE模型
class VQVAE(nn.Module):
    def __init__(self, input_dim=5, hidden_dim=64, num_embeddings=512, embedding_dim=5,
                 commitment_cost=0.25, decay=0.99, use_code_dim=False, code_size=100,
                 code_dim=16, code_dropout=0.1):
        """
        初始化VQ-VAE模型

        Args:
            input_dim: 输入向量维度
            hidden_dim: 隐藏层维度
            num_embeddings: 码本大小
            embedding_dim: 码向量维度
            commitment_cost: 承诺损失权重
            decay: EMA更新率
            use_code_dim: 是否使用证券代码维度
            code_size: 证券代码数量
            code_dim: 证券代码嵌入维度
            code_dropout: 证券代码嵌入dropout比例
        """
        super(VQVAE, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost
        self.use_code_dim = use_code_dim
        self.code_size = code_size
        self.code_dim = code_dim

        # 证券代码嵌入层
        if use_code_dim:
            self.code_embedding = nn.Embedding(code_size, code_dim)
            self.code_dropout = nn.Dropout(code_dropout)

        # 编码器 - 使用原始输入维度，不再增加证券代码嵌入维度
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, embedding_dim)
        )

        # 向量量化层
        self.vq_layer = VectorQuantizer(num_embeddings, embedding_dim, commitment_cost, decay)

        # 解码器 - 统一使用embedding_dim作为输入维度
        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, input_dim)
        )

    def forward(self, x, code_ids=None):
        """
        前向传播

        Args:
            x: 输入向量 [batch_size, input_dim]
            code_ids: 证券代码ID [batch_size]

        Returns:
            x_recon: 重建的输入向量 [batch_size, input_dim]
            indices: 码本索引 [batch_size]
            vq_loss: VQ损失
        """
        # 处理证券代码嵌入
        if self.use_code_dim and code_ids is not None:
            # 获取证券代码嵌入
            code_emb = self.code_embedding(code_ids)
            code_emb = self.code_dropout(code_emb)

            # 将证券代码嵌入与输入向量相加（使用0.1作为系数，与其他地方保持一致）
            # 确保维度匹配
            x = x + 0.1 * code_emb[:, :self.input_dim]

        # 编码
        z_e = self.encoder(x)
        z_q, indices, vq_loss = self.vq_layer(z_e)

        # 解码
        x_recon = self.decoder(z_q)

        return x_recon, indices, vq_loss

    def get_codebook(self):
        """获取码本权重"""
        return self.vq_layer.embeddings.weight.data


# 定义向量量化层
class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25, decay=0.99):
        super(VectorQuantizer, self).__init__()

        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost
        self.decay = decay

        # 初始化码本
        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        self.embeddings.weight.data.uniform_(-1.0 / num_embeddings, 1.0 / num_embeddings)

        # 使用EMA更新码本
        self.register_buffer('ema_cluster_size', torch.zeros(num_embeddings))
        self.register_buffer('ema_w', self.embeddings.weight.data.clone())

    def forward(self, x):
        # 计算输入向量与码本中所有码向量的距离
        # x: [batch_size, embedding_dim]
        # self.embeddings.weight: [num_embeddings, embedding_dim]
        distances = torch.sum(x**2, dim=1, keepdim=True) - 2 * torch.matmul(x, self.embeddings.weight.t()) + torch.sum(self.embeddings.weight**2, dim=1)

        # 找到最近的码向量
        encoding_indices = torch.argmin(distances, dim=1)

        # 获取量化后的向量
        z_q = self.embeddings(encoding_indices)

        # 计算VQ损失
        vq_loss = F.mse_loss(z_q, x.detach()) + self.commitment_cost * F.mse_loss(x, z_q.detach())

        # 使用EMA更新码本
        if self.training:
            # 计算每个码向量的使用频率
            encodings = F.one_hot(encoding_indices, self.num_embeddings).float()
            self.ema_cluster_size = self.ema_cluster_size * self.decay + (1 - self.decay) * torch.sum(encodings, dim=0)

            # 计算新的码向量
            dw = torch.matmul(encodings.t(), x)
            self.ema_w = self.ema_w * self.decay + (1 - self.decay) * dw

            # 更新码本
            n = torch.sum(self.ema_cluster_size)
            cluster_size = (self.ema_cluster_size + 1e-5) / (n + self.num_embeddings * 1e-5) * n
            self.embeddings.weight.data = self.ema_w / cluster_size.unsqueeze(1)

        # 使用直通估计器
        z_q = x + (z_q - x).detach()

        return z_q, encoding_indices, vq_loss


# 训练VQ-VAE模型
def train_vqvae(model, dataloader, optimizer, num_epochs, device, save_dir, save_interval=5, use_code_dim=False):
    """
    训练VQ-VAE模型

    Args:
        model: VQ-VAE模型
        dataloader: 数据加载器
        optimizer: 优化器
        num_epochs: 训练轮数
        device: 训练设备
        save_dir: 保存目录
        save_interval: 保存间隔
        use_code_dim: 是否使用证券代码维度
    """
    model.train()

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 记录训练损失
    train_losses = []
    recon_losses = []
    vq_losses = []

    for epoch in range(num_epochs):
        epoch_loss = 0
        epoch_recon_loss = 0
        epoch_vq_loss = 0

        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for batch_data in progress_bar:
            optimizer.zero_grad()

            # 处理批次数据
            if use_code_dim:
                # 如果使用证券代码维度，批次数据是一个元组 (vectors, code_ids)
                vectors, code_ids = batch_data
                vectors = vectors.to(device)
                code_ids = code_ids.to(device)

                # 前向传播
                recon_batch, _, vq_loss = model(vectors, code_ids)

                # 计算重建损失
                recon_loss = F.mse_loss(recon_batch, vectors)
            else:
                # 不使用证券代码维度，批次数据直接是向量
                vectors = batch_data.to(device)

                # 前向传播
                recon_batch, _, vq_loss = model(vectors)

                # 计算重建损失
                recon_loss = F.mse_loss(recon_batch, vectors)

            # 计算总损失
            loss = recon_loss + vq_loss

            # 反向传播
            loss.backward()
            optimizer.step()

            # 更新进度条
            epoch_loss += loss.item()
            epoch_recon_loss += recon_loss.item()
            epoch_vq_loss += vq_loss.item()
            progress_bar.set_postfix({
                'loss': epoch_loss / (progress_bar.n + 1),
                'recon_loss': epoch_recon_loss / (progress_bar.n + 1),
                'vq_loss': epoch_vq_loss / (progress_bar.n + 1)
            })

        # 记录损失
        avg_loss = epoch_loss / len(dataloader)
        avg_recon_loss = epoch_recon_loss / len(dataloader)
        avg_vq_loss = epoch_vq_loss / len(dataloader)

        train_losses.append(avg_loss)
        recon_losses.append(avg_recon_loss)
        vq_losses.append(avg_vq_loss)

        print(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.6f}, Recon Loss: {avg_recon_loss:.6f}, VQ Loss: {avg_vq_loss:.6f}")

        # 保存模型和码本权重
        if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
            # 保存完整模型
            model_path = os.path.join(save_dir, f"vqvae_model_epoch_{epoch+1}.pt")
            torch.save(model.state_dict(), model_path)

            # 保存码本权重
            codebook_weights = model.get_codebook()
            codebook_path = os.path.join(save_dir, f"vqvae_codebook_epoch_{epoch+1}.pt")
            torch.save(codebook_weights, codebook_path)

            print(f"保存模型到 {model_path}")
            print(f"保存码本权重到 {codebook_path}")

    # 绘制损失曲线
    plt.figure(figsize=(12, 4))

    plt.subplot(1, 3, 1)
    plt.plot(train_losses)
    plt.title('Total Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')

    plt.subplot(1, 3, 2)
    plt.plot(recon_losses)
    plt.title('Reconstruction Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')

    plt.subplot(1, 3, 3)
    plt.plot(vq_losses)
    plt.title('VQ Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')

    plt.tight_layout()

    # 保存损失曲线
    loss_plot_path = os.path.join(save_dir, "loss_plot.png")
    plt.savefig(loss_plot_path)
    print(f"保存损失曲线到 {loss_plot_path}")

    # 返回最终的码本权重和损失
    return model.get_codebook(), vq_losses[-1]


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='训练CandlestickVQTokenizer的码本权重')

    # 数据参数
    parser.add_argument('--data_dir', type=str, required=True, help='K线数据目录')
    parser.add_argument('--market', type=str, default='all', help='市场类型，如all, stock, future')
    parser.add_argument('--block_name', type=str, default=None, help='板块名称')
    parser.add_argument('--period', type=str, default='day', help='K线周期，如day, min1, min5')
    parser.add_argument('--output_name', type=str, default=None, help='输出文件名前缀')

    # 保存参数
    parser.add_argument('--save_dir', type=str, required=True, help='保存目录')

    # 训练参数
    parser.add_argument('--num_epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=128, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')

    # 模型参数
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=5, help='码向量维度')
    parser.add_argument('--hidden_dim', type=int, default=64, help='隐藏层维度')
    parser.add_argument('--use_code_dim', action='store_true', help='是否使用证券代码维度')
    parser.add_argument('--code_dim', type=int, default=6, help='证券代码嵌入维度')
    parser.add_argument('--code_dropout', type=float, default=0.1, help='证券代码嵌入dropout比例')

    # 数据处理参数
    parser.add_argument('--vectorization_method', type=str, default='atr_based', help='向量化方法')
    parser.add_argument('--atr_window', type=int, default=14, help='ATR计算窗口')
    parser.add_argument('--ma_volume_period', type=int, default=20, help='成交量移动平均周期')
    parser.add_argument('--include_volume', action='store_true', help='是否包含交易量')
    parser.add_argument('--min_samples', type=int, default=1000, help='最小样本数')
    parser.add_argument('--max_samples', type=int, default=100000, help='最大样本数')
    parser.add_argument('--max_samples_per_code', type=int, default=1000, help='每个证券代码的最大样本数')

    # 其他参数
    parser.add_argument('--save_interval', type=int, default=5, help='保存间隔')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='训练设备')

    args = parser.parse_args()
    print(args)

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d")
    save_dir = os.path.join(args.save_dir, f"vqvae_{timestamp}")
    os.makedirs(save_dir, exist_ok=True)

    # 查找数据文件
    data_files = get_data_files(args.data_dir, args.market, args.block_name, args.period)
    if len(data_files) == 0:
        print(f"错误: 在 {args.data_dir} 中未找到匹配的数据文件")
        return
    print(f"找到 {len(data_files)} 个数据文件")

    # 设置向量维度
    vector_dim = 5 if args.include_volume else 4

    # 创建数据集
    dataset = KLineDataset(
        data_files=data_files,
        num_embeddings=args.num_embeddings,
        atr_period=args.atr_window,
        ma_volume_period=args.ma_volume_period,
        vector_dim=vector_dim,
        min_samples=args.min_samples,
        max_samples=args.max_samples,
        max_samples_per_code=args.max_samples_per_code,
        vectorization_method=args.vectorization_method,
        use_code_dim=args.use_code_dim
    )

    # 创建数据加载器
    dataloader = DataLoader(
        dataset=dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    # 获取证券代码数量
    code_size = 100

    # 创建模型
    model = VQVAE(
        input_dim=vector_dim,
        hidden_dim=args.hidden_dim,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim,
        use_code_dim=args.use_code_dim,
        code_size=code_size,
        code_dim=args.code_dim,
        code_dropout=args.code_dropout
    )
    model.to(args.device)

    # 创建优化器
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)

    # 训练模型
    print(f"开始训练，设备: {args.device}")
    print(f"使用证券代码维度: {args.use_code_dim}")
    if args.use_code_dim:
        print(f"证券代码数量: {code_size}")
        print(f"证券代码嵌入维度: {args.code_dim}")

    codebook_weights, vq_loss = train_vqvae(
        model=model,
        dataloader=dataloader,
        optimizer=optimizer,
        num_epochs=args.num_epochs,
        device=args.device,
        save_dir=save_dir,
        save_interval=args.save_interval,
        use_code_dim=args.use_code_dim
    )

    # 保存最终的码本权重
    # current_time = datetime.now().strftime("%m%d%H")
    loss_str = f"{vq_loss:.4f}" if vq_loss else "unknown"
    output_name = args.output_name or f"vqcb_{args.vectorization_method}_{args.market}_{args.block_name}_{args.period}_{args.num_embeddings}_{loss_str}"

    # 保存参数
    with open(os.path.join(save_dir, f"{output_name}.json"), "w") as f:
        json.dump(vars(args), f, indent=4)

    final_codebook_path = os.path.join(save_dir, f"{output_name}.pt")
    torch.save(codebook_weights, final_codebook_path)
    print(f"保存最终码本权重到 {final_codebook_path}")

    # 创建编码器模型并导出为ONNX格式
    encoder_model = CodeAwareEncoder(
        codebook_weights,
        code_size=code_size,
        code_dim=args.code_dim
    )

    # 导出编码器为ONNX格式
    encoder_onnx_path = os.path.join(save_dir, f"{output_name}_encoder.onnx")
    if encoder_model.to_onnx(encoder_onnx_path, input_shape=(1, args.embedding_dim)):
        print(f"成功导出编码器模型为ONNX格式: {encoder_onnx_path}")
    else:
        print(f"导出编码器模型为ONNX格式失败")

    # 创建解码器模型并导出为ONNX格式
    decoder_model = CodeAwareDecoder(
        codebook_weights=codebook_weights,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim,
        code_size=code_size,
        code_dim=args.code_dim
    )

    # 导出解码器为ONNX格式
    decoder_onnx_path = os.path.join(save_dir, f"{output_name}_decoder.onnx")
    if decoder_model.to_onnx(decoder_onnx_path):
        print(f"成功导出解码器模型为ONNX格式: {decoder_onnx_path}")
    else:
        print(f"导出解码器模型为ONNX格式失败")

    # 验证编码器和解码器
    print("\n开始验证编码器和解码器...")

    # 创建随机测试向量
    test_vectors = torch.randn(10, args.embedding_dim)
    print(f"测试向量形状: {test_vectors.shape}")

    if args.use_code_dim:
        print("使用证券代码维度")
        code_id = get_code_id("RB9999.SC")
    else:
        code_id = None

    # 使用PyTorch模型进行编码和解码
    with torch.no_grad():
        # 编码
        pytorch_token_ids = []
        for vec in test_vectors:
            # 确保向量是一维的
            if vec.dim() > 1:
                vec = vec.squeeze()
            token_id = encoder_model(vec, code_id).item()
            pytorch_token_ids.append(token_id)
            print(f"PyTorch编码: 向量 {vec} -> Token ID {token_id}")

        # 解码
        pytorch_decoded_vectors = []
        for token_id in pytorch_token_ids:
            # 确保token_id是一个标量
            token_tensor = torch.tensor([token_id], dtype=torch.int64)
            decoded_vec = decoder_model(token_tensor, code_id).numpy()
            pytorch_decoded_vectors.append(decoded_vec[0])
            print(f"PyTorch解码: Token ID {token_id} -> 向量 {decoded_vec[0]}")

    # 使用ONNX模型进行编码和解码
    try:
        import onnxruntime as ort

        # 加载ONNX编码器
        encoder_session = ort.InferenceSession(encoder_onnx_path)
        encoder_input_name = encoder_session.get_inputs()[0].name
        if args.use_code_dim:
            code_input_name = encoder_session.get_inputs()[1].name

        # 加载ONNX解码器
        decoder_session = ort.InferenceSession(decoder_onnx_path)
        decoder_input_name = decoder_session.get_inputs()[0].name
        if args.use_code_dim:
            code_input_name = decoder_session.get_inputs()[1].name

        # 编码
        onnx_token_ids = []
        for i, vec in enumerate(test_vectors):
            # 准备输入 - 确保是二维的 [1, embedding_dim]
            vec_np = vec.numpy()
            if vec_np.ndim == 1:
                vec_np = vec_np.reshape(1, -1)

            onnx_input = {encoder_input_name: vec_np.astype(np.float32)}

            if args.use_code_dim:
                onnx_input[code_input_name] = np.array([code_id], dtype=np.int64)

            # 打印调试信息
            print(f"ONNX编码输入 {i}: 形状={vec_np.shape}, 类型={vec_np.dtype}")
            print(f"ONNX编码输入值: {vec_np}")

            # 运行推理
            try:
                onnx_output = encoder_session.run(None, onnx_input)
                token_id = onnx_output[0][0]
                onnx_token_ids.append(token_id)
                print(f"ONNX编码: 向量 -> Token ID {token_id}")
            except Exception as e:
                print(f"ONNX编码错误: {e}")
                # 使用随机token_id作为后备
                token_id = np.random.randint(0, args.num_embeddings)
                onnx_token_ids.append(token_id)
                print(f"使用随机Token ID: {token_id}")

        # 解码
        onnx_decoded_vectors = []
        for i, token_id in enumerate(onnx_token_ids):
            # 准备输入 - 确保是一维的 [1]
            token_np = np.array([token_id]).astype(np.int64)
            onnx_input = {decoder_input_name: token_np}

            if args.use_code_dim:
                onnx_input[code_input_name] = np.array([code_id], dtype=np.int64)

            # 打印调试信息
            print(f"ONNX解码输入 {i}: 值={token_np}, 类型={token_np.dtype}")

            # 运行推理
            try:
                onnx_output = decoder_session.run(None, onnx_input)
                decoded_vec = onnx_output[0][0]
                onnx_decoded_vectors.append(decoded_vec)
                print(f"ONNX解码: Token ID {token_id} -> 向量 {decoded_vec}")
            except Exception as e:
                print(f"ONNX解码错误: {e}")
                # 使用随机向量作为后备
                random_vec = np.random.randn(args.embedding_dim).astype(np.float32)
                onnx_decoded_vectors.append(random_vec)
                print(f"使用随机向量: {random_vec}")

        # 比较结果
        pytorch_token_ids = np.array(pytorch_token_ids)
        onnx_token_ids = np.array(onnx_token_ids)

        token_match_count = np.sum(pytorch_token_ids == onnx_token_ids)
        token_match_rate = token_match_count / len(pytorch_token_ids) * 100

        print(f"\n编码匹配率: {token_match_rate:.2f}% ({token_match_count}/{len(pytorch_token_ids)})")
        print(f"PyTorch Token IDs: {pytorch_token_ids}")
        print(f"ONNX Token IDs: {onnx_token_ids}")

        # 计算解码向量的差异
        pytorch_decoded_vectors = np.array(pytorch_decoded_vectors)
        onnx_decoded_vectors = np.array(onnx_decoded_vectors)

        vector_diff = np.abs(pytorch_decoded_vectors - onnx_decoded_vectors)
        mean_diff = np.mean(vector_diff)

        print(f"\n解码平均差异: {mean_diff:.6f}")
        print(f"PyTorch解码示例: {pytorch_decoded_vectors[0]}")
        print(f"ONNX解码示例: {onnx_decoded_vectors[0]}")

        # 保存验证结果
        validation_path = os.path.join(save_dir, f"{output_name}_validation.txt")
        with open(validation_path, "w") as f:
            f.write(f"编码匹配率: {token_match_rate:.2f}% ({token_match_count}/{len(pytorch_token_ids)})\n")
            f.write(f"解码平均差异: {mean_diff:.6f}\n\n")

            f.write("测试向量:\n")
            for i, vec in enumerate(test_vectors):
                f.write(f"{i}: {vec.numpy()}\n")

            f.write("\nPyTorch Token IDs:\n")
            f.write(f"{pytorch_token_ids}\n")

            f.write("\nONNX Token IDs:\n")
            f.write(f"{onnx_token_ids}\n")

            f.write("\nPyTorch解码向量:\n")
            for i, vec in enumerate(pytorch_decoded_vectors):
                f.write(f"{i}: {vec}\n")

            f.write("\nONNX解码向量:\n")
            for i, vec in enumerate(onnx_decoded_vectors):
                f.write(f"{i}: {vec}\n")

        print(f"\n验证结果已保存到 {validation_path}")

    except Exception as e:
        print(f"验证ONNX模型时出错: {e}")


if __name__ == "__main__":
    main()
