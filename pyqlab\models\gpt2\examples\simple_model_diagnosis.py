"""
简化的模型诊断脚本

专注于分析模型为什么总是预测相同token的核心问题。
"""

import os
import sys
import numpy as np
import torch
import torch.nn.functional as F
from collections import Counter

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from backtest_candlestick_vq_gpt import create_tokenizer, load_model
from pyqlab.models.gpt2.utils import load_single_data, extract_time_features


def analyze_model_predictions(model, tokenizer, data_samples, device, num_tests=10):
    """分析模型预测的多样性"""
    print("=== 模型预测多样性分析 ===")

    model.eval()
    all_predictions = []
    all_logits_stats = []

    with torch.no_grad():
        for i in range(min(num_tests, len(data_samples))):
            df, code_id = data_samples[i]

            if len(df) < 50:
                continue

            # 测试不同的序列段
            for start_idx in [0, 10, 20, 30]:
                if start_idx + 30 > len(df):
                    continue

                sub_df = df.iloc[start_idx:start_idx+30]
                tokens = tokenizer.encode(sub_df, code_id=code_id)

                if len(tokens) < 30:
                    continue

                input_tokens = torch.tensor(tokens, dtype=torch.int32).unsqueeze(0).to(device)
                code_ids = torch.tensor([code_id], dtype=torch.int32).to(device)

                # 不使用时间特征以避免维度问题
                logits, _ = model(input_tokens, code_ids, time_features=None)
                last_logits = logits[0, -1]

                # 分析logits统计
                logits_stats = {
                    'mean': last_logits.mean().item(),
                    'std': last_logits.std().item(),
                    'min': last_logits.min().item(),
                    'max': last_logits.max().item(),
                    'range': (last_logits.max() - last_logits.min()).item()
                }
                all_logits_stats.append(logits_stats)

                # 计算概率分布
                probs = F.softmax(last_logits, dim=-1).cpu().numpy()
                top_5_indices = np.argsort(probs)[::-1][:5]
                top_5_probs = probs[top_5_indices]

                entropy = -np.sum(probs * np.log(probs + 1e-10))

                all_predictions.append({
                    'sample': i,
                    'start_idx': start_idx,
                    'top_5_indices': top_5_indices.tolist(),
                    'top_5_probs': top_5_probs.tolist(),
                    'entropy': entropy,
                    'max_prob': top_5_probs[0]
                })

                print(f"样本{i}-{start_idx}: Top-5={top_5_indices}, 最高概率={top_5_probs[0]:.4f}, 熵={entropy:.4f}")

    # 分析结果
    print(f"\n=== 预测统计分析 ===")
    print(f"总测试数量: {len(all_predictions)}")

    if len(all_predictions) == 0:
        print("⚠️  警告: 没有找到有效的预测数据")
        return {
            'prediction_diversity': 0.0,
            'avg_max_prob': 0.0,
            'avg_entropy': 0.0,
            'most_common_tokens': [],
            'logits_stats': {}
        }

    # 分析Top-1预测多样性
    all_top1 = [pred['top_5_indices'][0] for pred in all_predictions]
    unique_top1 = len(set(all_top1))
    print(f"唯一Top-1预测数量: {unique_top1}")
    print(f"预测多样性: {unique_top1/len(all_predictions):.2%}")

    # 统计最常见的预测
    top1_counter = Counter(all_top1)
    print(f"\n最常见的Top-1预测:")
    for token_id, count in top1_counter.most_common(10):
        percentage = count / len(all_predictions)
        print(f"  Token {token_id}: {count}次 ({percentage:.2%})")

    # 分析概率分布
    max_probs = [pred['max_prob'] for pred in all_predictions]
    entropies = [pred['entropy'] for pred in all_predictions]

    print(f"\n概率分布统计:")
    print(f"  最高概率 - 平均: {np.mean(max_probs):.4f}, 标准差: {np.std(max_probs):.4f}")
    print(f"  熵值 - 平均: {np.mean(entropies):.4f}, 标准差: {np.std(entropies):.4f}")
    print(f"  最大可能熵值: {np.log(tokenizer.vocab_size):.4f}")

    # 分析logits统计
    print(f"\nLogits统计:")
    avg_stats = {
        'mean': np.mean([s['mean'] for s in all_logits_stats]),
        'std': np.mean([s['std'] for s in all_logits_stats]),
        'range': np.mean([s['range'] for s in all_logits_stats])
    }
    print(f"  平均均值: {avg_stats['mean']:.6f}")
    print(f"  平均标准差: {avg_stats['std']:.6f}")
    print(f"  平均动态范围: {avg_stats['range']:.6f}")

    return {
        'prediction_diversity': unique_top1 / len(all_predictions),
        'avg_max_prob': np.mean(max_probs),
        'avg_entropy': np.mean(entropies),
        'most_common_tokens': top1_counter.most_common(5),
        'logits_stats': avg_stats
    }


def analyze_input_tokens(data_samples, tokenizer, num_samples=10):
    """分析输入token的多样性"""
    print(f"\n=== 输入Token多样性分析 ===")

    all_tokens = []
    all_sequences = []

    for i, (df, code_id) in enumerate(data_samples[:num_samples]):
        if len(df) < 50:
            continue

        # 分析多个序列段
        for start_idx in range(0, len(df) - 30, 10):
            sub_df = df.iloc[start_idx:start_idx+30]
            tokens = tokenizer.encode(sub_df, code_id=code_id)

            if len(tokens) == 30:
                all_sequences.append(tuple(tokens))
                all_tokens.extend(tokens)

    print(f"分析了 {len(all_sequences)} 个序列")

    if len(all_sequences) == 0:
        print("⚠️  警告: 没有找到有效的序列数据")
        return {
            'sequence_diversity': 0.0,
            'vocab_utilization': 0.0,
            'most_common_input_tokens': []
        }

    # 序列多样性
    unique_sequences = len(set(all_sequences))
    print(f"唯一序列数量: {unique_sequences} / {len(all_sequences)} ({unique_sequences/len(all_sequences):.2%})")

    # Token分布
    token_counter = Counter(all_tokens)
    print(f"\nToken分布:")
    print(f"  总token数: {len(all_tokens)}")
    print(f"  唯一token数: {len(token_counter)}")
    print(f"  词汇表利用率: {len(token_counter)/tokenizer.vocab_size:.2%}")

    # 最常见的tokens
    print(f"\n最常见的tokens:")
    for token_id, count in token_counter.most_common(10):
        percentage = count / len(all_tokens)
        print(f"  Token {token_id}: {count}次 ({percentage:.2%})")

    return {
        'sequence_diversity': unique_sequences / len(all_sequences),
        'vocab_utilization': len(token_counter) / tokenizer.vocab_size,
        'most_common_input_tokens': token_counter.most_common(10)
    }


def analyze_model_weights(model):
    """分析模型权重"""
    print(f"\n=== 模型权重分析 ===")

    # Token embedding
    token_emb = model.token_embedding.weight.data
    print(f"Token embedding:")
    print(f"  形状: {token_emb.shape}")
    print(f"  均值: {token_emb.mean().item():.6f}")
    print(f"  标准差: {token_emb.std().item():.6f}")

    # 输出层权重
    head_weight = model.head.weight.data
    print(f"\n输出层权重:")
    print(f"  形状: {head_weight.shape}")
    print(f"  均值: {head_weight.mean().item():.6f}")
    print(f"  标准差: {head_weight.std().item():.6f}")

    # 检查权重是否异常
    issues = []
    if torch.isnan(token_emb).any():
        issues.append("Token embedding包含NaN值")
    if torch.isnan(head_weight).any():
        issues.append("输出层权重包含NaN值")
    if token_emb.std() < 1e-6:
        issues.append("Token embedding标准差过小")
    if head_weight.std() < 1e-6:
        issues.append("输出层权重标准差过小")

    return {
        'token_emb_std': token_emb.std().item(),
        'head_weight_std': head_weight.std().item(),
        'issues': issues
    }


def main():
    """主函数"""
    print("🔍 简化模型诊断")
    print("=" * 50)

    # 模型和数据路径
    model_path = "E:/lab/RoboQuant/pylab/models/candlestick_vq_gpt/best_model.pt"
    codebook_path = "E:/lab/RoboQuant/pylab/models/vqvae/vqvae_20250522/vqcb_atr_based_fut_top_min1_512_0.0201.pt"
    data_path = "f:/hqdata/tsdb/fut_top_min1.parquet"

    # 创建参数对象
    class Args:
        def __init__(self):
            self.model_path = model_path
            self.codebook_path = codebook_path
            self.vectorization_method = 'atr_based'
            self.use_code_dim = True
            self.code_dim = 5
            self.seq_len = 30
            self.use_time_features = False  # 暂时禁用时间特征

    args = Args()

    # 创建tokenizer和加载模型
    print("创建Tokenizer...")
    tokenizer = create_tokenizer(args)
    print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")

    print("加载模型...")
    model, device = load_model(args, tokenizer)
    print(f"模型加载成功，设备: {device}")

    # 加载数据
    print("加载数据...")
    data, code_ids = load_single_data(data_path, begin_date="2025-04-25", end_date="2025-04-30")
    data_samples = [(df, code_ids[i]) for i, df in enumerate(data) if len(df) >= 50]
    print(f"有效数据样本: {len(data_samples)}")

    # 运行诊断
    results = {}

    # 1. 模型权重分析
    results['weights'] = analyze_model_weights(model)

    # 2. 输入token多样性分析
    results['input_diversity'] = analyze_input_tokens(data_samples, tokenizer, 5)

    # 3. 模型预测分析
    results['predictions'] = analyze_model_predictions(model, tokenizer, data_samples, device, 5)

    # 生成诊断报告
    print("\n" + "=" * 50)
    print("🔍 诊断报告")
    print("=" * 50)

    issues = []

    # 检查各种问题
    if results['predictions']['prediction_diversity'] < 0.1:
        issues.append(f"预测多样性极低: {results['predictions']['prediction_diversity']:.1%}")

    if results['predictions']['avg_max_prob'] > 0.5:
        issues.append(f"预测过于确定: 平均最高概率{results['predictions']['avg_max_prob']:.1%}")

    if results['input_diversity']['sequence_diversity'] < 0.3:
        issues.append(f"输入序列多样性低: {results['input_diversity']['sequence_diversity']:.1%}")

    if results['input_diversity']['vocab_utilization'] < 0.3:
        issues.append(f"词汇表利用率低: {results['input_diversity']['vocab_utilization']:.1%}")

    if results['weights']['issues']:
        issues.extend(results['weights']['issues'])

    if issues:
        print("❌ 发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")

        print("\n💡 可能的原因和解决方案:")
        if results['predictions']['prediction_diversity'] < 0.1:
            print("  - 模型过拟合或训练不充分")
            print("  - 建议: 重新训练模型，增加正则化")

        if results['input_diversity']['sequence_diversity'] < 0.3:
            print("  - 输入数据缺乏多样性")
            print("  - 建议: 检查数据预处理和tokenization过程")

        if results['predictions']['avg_max_prob'] > 0.5:
            print("  - 模型输出过于确定性")
            print("  - 建议: 使用更高的温度参数或不同的采样策略")

        print("\n🔧 立即可尝试的解决方案:")
        print("  1. 使用更高的温度参数 (temperature=2.0-5.0)")
        print("  2. 使用更小的top_k值 (top_k=10-20)")
        print("  3. 检查训练数据的质量和多样性")
        print("  4. 考虑重新训练模型")
    else:
        print("✅ 未发现明显问题")

    print(f"\n📊 关键指标:")
    print(f"  预测多样性: {results['predictions']['prediction_diversity']:.2%}")
    print(f"  平均最高概率: {results['predictions']['avg_max_prob']:.2%}")
    print(f"  平均熵值: {results['predictions']['avg_entropy']:.4f}")
    print(f"  输入序列多样性: {results['input_diversity']['sequence_diversity']:.2%}")
    print(f"  词汇表利用率: {results['input_diversity']['vocab_utilization']:.2%}")


if __name__ == "__main__":
    main()
