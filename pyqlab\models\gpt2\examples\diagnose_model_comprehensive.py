"""
全面诊断CandlestickVQGPT模型问题

该脚本用于深入分析模型为什么总是预测相同的token，
包括模型权重、输入数据、码本质量等多个方面的诊断。
"""

import os
import sys
import argparse
import json
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
from collections import Counter

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.utils import load_single_data, extract_time_features


def analyze_model_internal_state(model, sample_input, device):
    """分析模型内部状态"""
    print("\n=== 模型内部状态分析 ===")

    model.eval()
    input_tokens, code_ids, time_features = sample_input

    with torch.no_grad():
        # 获取token embedding
        token_emb = model.token_embedding(input_tokens)
        print(f"Token embedding统计:")
        print(f"  形状: {token_emb.shape}")
        print(f"  均值: {token_emb.mean().item():.6f}")
        print(f"  标准差: {token_emb.std().item():.6f}")
        print(f"  最小值: {token_emb.min().item():.6f}")
        print(f"  最大值: {token_emb.max().item():.6f}")

        # 获取旋转位置编码
        seq_len = input_tokens.size(1)
        cos, sin = model.rotary_emb(token_emb, seq_len=seq_len)
        print(f"\n旋转位置编码统计:")
        print(f"  cos形状: {cos.shape}")
        print(f"  sin形状: {sin.shape}")
        print(f"  cos均值: {cos.mean().item():.6f}")
        print(f"  sin均值: {sin.mean().item():.6f}")
        print(f"  cos标准差: {cos.std().item():.6f}")
        print(f"  sin标准差: {sin.std().item():.6f}")

        # 检查不同位置的位置编码是否不同
        if cos.size(2) > 1:
            cos_diff = torch.abs(cos[0, 0, 1:] - cos[0, 0, :-1]).mean()
            sin_diff = torch.abs(sin[0, 0, 1:] - sin[0, 0, :-1]).mean()
            print(f"  相邻位置cos差异: {cos_diff.item():.6f}")
            print(f"  相邻位置sin差异: {sin_diff.item():.6f}")

            pos_diff = (cos_diff + sin_diff) / 2
            if pos_diff < 1e-6:
                print("  ⚠️  警告: 位置编码几乎相同，可能存在问题！")
        else:
            pos_diff = 1.0  # 单个位置，假设正常

        # 获取代码embedding
        if hasattr(model, 'code_embedding') and code_ids is not None:
            code_emb = model.code_embedding(code_ids)
            print(f"\n代码embedding统计:")
            print(f"  形状: {code_emb.shape}")
            print(f"  均值: {code_emb.mean().item():.6f}")
            print(f"  标准差: {code_emb.std().item():.6f}")

        # 分析transformer层的输入
        x = token_emb.clone()
        if hasattr(model, 'code_embedding') and code_ids is not None:
            code_emb_expanded = code_emb.unsqueeze(1).expand(-1, seq_len, -1)
            x = x + code_emb_expanded

        # 添加时间特征
        if hasattr(model, 'time_embedding') and time_features is not None:
            time_emb = model.time_embedding(time_features)
            x = x + time_emb

        print(f"\n输入到transformer的特征:")
        print(f"  均值: {x.mean().item():.6f}")
        print(f"  标准差: {x.std().item():.6f}")

        # 逐层分析transformer
        for i, block in enumerate(model.transformer.h):
            x_before = x.clone()
            x = block(x)

            # 计算层的变化
            layer_change = torch.abs(x - x_before).mean()
            print(f"  第{i+1}层变化: {layer_change.item():.6f}")

            if layer_change < 1e-6:
                print(f"    ⚠️  警告: 第{i+1}层几乎没有变化！")

        # 最终输出分析
        logits = model.head(model.ln_f(x))
        print(f"\n最终logits统计:")
        print(f"  形状: {logits.shape}")
        print(f"  均值: {logits.mean().item():.6f}")
        print(f"  标准差: {logits.std().item():.6f}")

        # 分析最后一个时间步的logits分布
        last_logits = logits[0, -1]
        print(f"\n最后时间步logits分析:")
        print(f"  最大值: {last_logits.max().item():.6f}")
        print(f"  最小值: {last_logits.min().item():.6f}")
        print(f"  动态范围: {(last_logits.max() - last_logits.min()).item():.6f}")

        # 检查是否存在异常值
        top_5_values, top_5_indices = torch.topk(last_logits, 5)
        print(f"  Top-5 logits: {top_5_values.cpu().numpy()}")
        print(f"  Top-5 indices: {top_5_indices.cpu().numpy()}")

        return {
            'token_emb_stats': {
                'mean': token_emb.mean().item(),
                'std': token_emb.std().item()
            },
            'pos_emb_diff': pos_diff.item(),
            'final_logits_range': (last_logits.max() - last_logits.min()).item(),
            'top_5_indices': top_5_indices.cpu().numpy().tolist()
        }


def analyze_input_diversity(data_samples, tokenizer, num_samples=50):
    """分析输入数据多样性"""
    print(f"\n=== 输入数据多样性分析 ===")

    all_sequences = []
    all_tokens = []

    for i, (df, code_id) in enumerate(data_samples[:num_samples]):
        if len(df) < 50:
            continue

        # 分析多个不同的序列
        for start_idx in range(0, len(df) - 30, 10):
            sub_df = df.iloc[start_idx:start_idx+30]
            tokens = tokenizer.encode(sub_df, code_id=code_id)

            if len(tokens) == 30:
                all_sequences.append(tokens)
                all_tokens.extend(tokens)

    print(f"分析了 {len(all_sequences)} 个序列")

    # 分析序列多样性
    unique_sequences = len(set(tuple(seq) for seq in all_sequences))
    print(f"唯一序列数量: {unique_sequences} / {len(all_sequences)} ({unique_sequences/len(all_sequences):.2%})")

    if unique_sequences < len(all_sequences) * 0.5:
        print("⚠️  警告: 序列多样性过低！很多序列是重复的。")

    # 分析token分布
    token_counter = Counter(all_tokens)
    print(f"\nToken分布分析:")
    print(f"  总token数: {len(all_tokens)}")
    print(f"  唯一token数: {len(token_counter)}")
    print(f"  词汇表利用率: {len(token_counter)/tokenizer.vocab_size:.2%}")

    # 检查是否存在过度集中的token
    most_common = token_counter.most_common(10)
    print(f"\n最常见的tokens:")
    for token_id, count in most_common:
        percentage = count / len(all_tokens)
        print(f"  Token {token_id}: {count}次 ({percentage:.2%})")

        if percentage > 0.1:  # 如果某个token占比超过10%
            print(f"    ⚠️  警告: Token {token_id} 出现频率过高！")

    return {
        'sequence_diversity': unique_sequences / len(all_sequences),
        'vocab_utilization': len(token_counter) / tokenizer.vocab_size,
        'most_common_tokens': most_common[:5]
    }


def analyze_codebook_quality(tokenizer):
    """分析码本质量"""
    print(f"\n=== 码本质量分析 ===")

    if not hasattr(tokenizer, 'codebook'):
        print("无法访问码本，跳过分析")
        return {}

    codebook = tokenizer.codebook
    print(f"码本形状: {codebook.shape}")

    # 分析码本向量的多样性
    codebook_np = codebook.cpu().numpy()

    # 计算向量间的距离
    from sklearn.metrics.pairwise import cosine_distances
    distances = cosine_distances(codebook_np)

    # 排除对角线（自己与自己的距离）
    mask = np.eye(len(distances), dtype=bool)
    distances_no_diag = distances[~mask]

    print(f"码本向量间距离统计:")
    print(f"  平均距离: {distances_no_diag.mean():.6f}")
    print(f"  最小距离: {distances_no_diag.min():.6f}")
    print(f"  最大距离: {distances_no_diag.max():.6f}")
    print(f"  标准差: {distances_no_diag.std():.6f}")

    # 检查是否有过于相似的向量
    min_distance_threshold = 0.1
    similar_pairs = np.sum(distances_no_diag < min_distance_threshold)
    total_pairs = len(distances_no_diag)

    print(f"相似向量对数量: {similar_pairs} / {total_pairs} ({similar_pairs/total_pairs:.2%})")

    if similar_pairs / total_pairs > 0.1:
        print("⚠️  警告: 码本中有太多相似的向量！")

    return {
        'avg_distance': distances_no_diag.mean(),
        'min_distance': distances_no_diag.min(),
        'similar_pairs_ratio': similar_pairs / total_pairs
    }


def test_model_with_different_inputs(model, tokenizer, data_samples, device, num_tests=10):
    """测试模型对不同输入的响应"""
    print(f"\n=== 测试模型对不同输入的响应 ===")

    model.eval()
    predictions = []

    with torch.no_grad():
        for i in range(min(num_tests, len(data_samples))):
            df, code_id = data_samples[i]

            if len(df) < 50:
                continue

            # 测试不同的序列段
            for start_idx in [0, 10, 20]:
                if start_idx + 30 > len(df):
                    continue

                sub_df = df.iloc[start_idx:start_idx+30]
                tokens = tokenizer.encode(sub_df, code_id=code_id)

                if len(tokens) < 30:
                    continue

                input_tokens = torch.tensor(tokens, dtype=torch.int32).unsqueeze(0).to(device)
                code_ids = torch.tensor([code_id], dtype=torch.int32).to(device)
                time_features = extract_time_features(sub_df)
                time_features = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(device)

                logits, _ = model(input_tokens, code_ids, time_features)
                last_logits = logits[0, -1]

                probs = F.softmax(last_logits, dim=-1).cpu().numpy()
                top_5_indices = np.argsort(probs)[::-1][:5]
                top_5_probs = probs[top_5_indices]

                predictions.append({
                    'sample': i,
                    'start_idx': start_idx,
                    'top_5_indices': top_5_indices.tolist(),
                    'top_5_probs': top_5_probs.tolist(),
                    'entropy': -np.sum(probs * np.log(probs + 1e-10))
                })

                print(f"样本{i}-{start_idx}: Top-5 = {top_5_indices}, 熵 = {predictions[-1]['entropy']:.4f}")

    # 分析预测多样性
    all_top1 = [pred['top_5_indices'][0] for pred in predictions]
    unique_top1 = len(set(all_top1))

    print(f"\n预测多样性分析:")
    print(f"  测试数量: {len(predictions)}")
    print(f"  唯一Top-1预测: {unique_top1}")
    print(f"  预测多样性: {unique_top1/len(predictions):.2%}")

    # 统计最常见的预测
    top1_counter = Counter(all_top1)
    print(f"\n最常见的Top-1预测:")
    for token_id, count in top1_counter.most_common(5):
        print(f"  Token {token_id}: {count}次 ({count/len(predictions):.2%})")

    return {
        'prediction_diversity': unique_top1 / len(predictions),
        'most_common_predictions': top1_counter.most_common(5)
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='全面诊断CandlestickVQGPT模型问题')

    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本文件路径')
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--vectorization_method', type=str, default='atr_based',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    parser.add_argument('--num_samples', type=int, default=20, help='分析的样本数量')
    parser.add_argument('--use_code_dim', action='store_true', help='是否使用代码维度')
    parser.add_argument('--code_dim', type=int, default=16, help='代码维度大小')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')

    args = parser.parse_args()

    print("🔍 开始全面诊断模型问题...")
    print("=" * 60)

    # 创建tokenizer和加载模型
    from backtest_candlestick_vq_gpt import create_tokenizer, load_model

    tokenizer = create_tokenizer(args)
    model, device = load_model(args, tokenizer)

    # 加载数据
    data, code_ids = load_single_data(args.data_path, begin_date="2025-04-25", end_date="2025-04-30")
    data_samples = [(df, code_ids[i]) for i, df in enumerate(data) if len(df) >= 50]

    print(f"加载了 {len(data_samples)} 个有效数据样本")

    # 准备样本输入
    df, code_id = data_samples[0]
    tokens = tokenizer.encode(df.iloc[:30], code_id=code_id)
    input_tokens = torch.tensor(tokens, dtype=torch.int32).unsqueeze(0).to(device)
    code_ids_tensor = torch.tensor([code_id], dtype=torch.int32).to(device)
    time_features = extract_time_features(df.iloc[:30])
    time_features_tensor = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(device)

    sample_input = (input_tokens, code_ids_tensor, time_features_tensor)

    # 运行诊断
    results = {}

    # 1. 模型内部状态分析
    results['model_internal'] = analyze_model_internal_state(model, sample_input, device)

    # 2. 输入数据多样性分析
    results['input_diversity'] = analyze_input_diversity(data_samples, tokenizer, args.num_samples)

    # 3. 码本质量分析
    results['codebook_quality'] = analyze_codebook_quality(tokenizer)

    # 4. 不同输入的响应测试
    results['response_diversity'] = test_model_with_different_inputs(model, tokenizer, data_samples, device)

    # 生成诊断报告
    print("\n" + "=" * 60)
    print("🔍 诊断报告总结")
    print("=" * 60)

    issues = []

    # 检查各种问题
    if results['model_internal']['pos_emb_diff'] < 1e-6:
        issues.append("位置编码失效 - 所有位置的编码几乎相同")

    if results['input_diversity']['sequence_diversity'] < 0.5:
        issues.append(f"输入序列多样性过低 - 只有{results['input_diversity']['sequence_diversity']:.1%}的序列是唯一的")

    if results['input_diversity']['vocab_utilization'] < 0.3:
        issues.append(f"词汇表利用率过低 - 只使用了{results['input_diversity']['vocab_utilization']:.1%}的词汇")

    if results['response_diversity']['prediction_diversity'] < 0.1:
        issues.append(f"模型预测多样性极低 - 只有{results['response_diversity']['prediction_diversity']:.1%}的预测是不同的")

    if 'similar_pairs_ratio' in results['codebook_quality'] and results['codebook_quality']['similar_pairs_ratio'] > 0.1:
        issues.append(f"码本质量问题 - {results['codebook_quality']['similar_pairs_ratio']:.1%}的向量对过于相似")

    if issues:
        print("❌ 发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")

        print("\n💡 建议的解决方案:")
        print("  1. 重新训练模型，使用更多样化的数据")
        print("  2. 检查并重新训练VQ码本")
        print("  3. 调整模型架构（增加层数、注意力头数）")
        print("  4. 使用更强的正则化（dropout, weight decay）")
        print("  5. 检查数据预处理和tokenization过程")
        print("  6. 考虑使用不同的损失函数（focal loss, label smoothing）")
    else:
        print("✅ 未发现明显的结构性问题")
        print("   问题可能出现在训练过程或数据质量上")

    # 保存诊断结果
    output_file = "model_diagnosis_report.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"\n📄 详细诊断结果已保存到: {output_file}")


if __name__ == "__main__":
    main()
