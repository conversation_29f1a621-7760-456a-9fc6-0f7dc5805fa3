from typing import List
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer

class CandlestickDataset(Dataset):
    """K线数据集"""
    def __init__(self, data: List[pd.DataFrame], code_ids: List[int], tokenizer: CandlestickVQTokenizer, seq_len=30, stride=1):
        """
        初始化数据集

        Args:
            data: 列表，每个元素是一个证券的K线数据DataFrame
            code_ids: 列表，每个元素是一个证券的代码ID
            tokenizer: CandlestickVQTokenizer实例
            seq_len: 输入序列长度
            stride: 滑动窗口步长
        """
        self.data = data
        self.code_ids = code_ids
        self.tokenizer = tokenizer
        self.seq_len = seq_len
        self.stride = stride

        # 预处理数据
        self.samples = self._preprocess_data()

    def _preprocess_data(self):
        """预处理数据，生成样本"""

        print(f"开始预处理数据，共 {len(self.data)} 个证券： {self.code_ids}...")
        samples = []

        for i, df in enumerate(self.data):
            print(f"处理证券 {i}: {self.code_ids[i]}")
            if len(df) < self.seq_len:
                continue

            code_id = self.code_ids[i]

            # 对整个数据进行tokenize
            processed_df = self.tokenizer._preprocess_df(df)

            # 生成token序列
            token_ids = []
            for j in range(len(processed_df)):
                row = processed_df.iloc[j]
                # 检查是否有足够的历史数据
                if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
                    token_ids.append(self.tokenizer.unk_token_id)
                    continue

                token_id = self.tokenizer.tokenize_single_candlestick(
                    row,
                    row['Prev_Close'],
                    row['MA_Volume'],
                    row['ATR'],
                    code_id=code_id
                )
                token_ids.append(token_id)

            # 生成时间特征
            time_features = self._extract_time_features(df)

            # 生成样本
            for j in range(0, len(token_ids) - self.seq_len - 1, self.stride):
                # 输入序列
                input_seq = token_ids[j:j+self.seq_len]
                # 目标序列
                target_seq = token_ids[j+1:j+self.seq_len+1]  # 移位一位作为目标
                # 时间特征
                time_feat = time_features[j:j+self.seq_len]

                samples.append({
                    'input_tokens': input_seq,
                    'target_tokens': target_seq,
                    'code_id': code_id,
                    'time_features': time_feat
                })

        return samples

    # --- 时间特征辅助函数 ---
    def _cyclical_encoding(self, value, max_val):
        """对周期性特征进行 sin/cos 编码"""
        sin = np.sin(2 * np.pi * value / max_val)
        cos = np.cos(2 * np.pi * value / max_val)
        return sin, cos

    def _extract_time_features(self, df):
        """
        从 datetime 对象提取特征向量。
        你可以根据需要添加/修改特征。
        """
        features = []
        for dt in df['datetime']:
            # 示例特征：小时, 星期几, 月份, 年份中的第几天
            minute = dt.hour * 60 + dt.minute
            hour = dt.hour
            day_of_week = (dt.weekday() + 1) % 7  # Sunday=0, Monday=1, ..., Saturday=6
            month = dt.month
            # day_of_year = dt.timetuple().tm_yday

            # 进行周期编码
            minute_sin, minute_cos = self._cyclical_encoding(minute, 1440)
            hour_sin, hour_cos = self._cyclical_encoding(hour, 24)
            dow_sin, dow_cos = self._cyclical_encoding(day_of_week, 7)
            month_sin, month_cos = self._cyclical_encoding(month, 12)
            # doy_sin, doy_cos = self._cyclical_encoding(day_of_year, 366) # 考虑闰年

            # 组合特征 (可以添加其他非周期特征，如年份，并进行归一化)
            # 这里只用了周期特征作为示例
            feature = [
                minute_sin, minute_cos,
                hour_sin, hour_cos,
                dow_sin, dow_cos,
                month_sin, month_cos,
                # doy_sin, doy_cos
                ]
            features.append(feature)
        return np.array(features, dtype=np.float32)

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]

        # 转换为tensor
        input_tensor = torch.tensor(sample['input_tokens'], dtype=torch.long)
        target_tensor = torch.tensor(sample['target_tokens'], dtype=torch.long)
        code_id_tensor = torch.tensor(sample['code_id'], dtype=torch.long)
        time_features_tensor = torch.tensor(sample['time_features'], dtype=torch.float)
        return input_tensor, target_tensor, code_id_tensor, time_features_tensor
