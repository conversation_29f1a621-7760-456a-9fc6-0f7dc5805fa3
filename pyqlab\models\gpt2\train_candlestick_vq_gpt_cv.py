"""
使用交叉验证训练CandlestickVQGPT模型
"""

import os
import argparse
import json
import time
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.optim import Adam<PERSON>
from torch.optim.lr_scheduler import OneCycleLR
from torch.cuda.amp import GradScaler
from torch.utils.data import DataLoader, Subset
from sklearn.model_selection import KFold
from tqdm.auto import tqdm
import matplotlib.pyplot as plt

from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.candlestick_vq_dataset import CandlestickDataset
from pyqlab.models.gpt2.utils import get_data_files, load_single_data

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_tokenizer(args):
    """创建Tokenizer"""
    logger.info("创建Tokenizer...")

    # 选择向量化方法
    if args.vectorization_method == 'atr_based':
        vectorization_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vectorization_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vectorization_method = VectorizationMethod.LOG_RETURN
    elif args.vectorization_method == 'zscore':
        vectorization_method = VectorizationMethod.ZSCORE
    elif args.vectorization_method == 'minmax':
        vectorization_method = VectorizationMethod.MINMAX
    else:
        raise ValueError(f"未知的向量化方法: {args.vectorization_method}")

    # 创建Tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim,
        atr_period=args.atr_period,
        ma_volume_period=args.ma_volume_period,
        vectorization_method=vectorization_method,
        detect_gaps=True,
        gap_threshold=2.0,
        use_code_dim=args.use_code_dim,
        code_size=args.code_size,
        code_dim=args.code_dim
    )

    logger.info(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")

    return tokenizer

def create_datasets(data, code_ids, tokenizer, args):
    """创建数据集"""
    logger.info("创建数据集...")

    # 创建数据集
    dataset = CandlestickDataset(
        data=data,
        code_ids=code_ids,
        tokenizer=tokenizer,
        seq_len=args.seq_len,
        stride=args.stride,
        # use_time_features=args.use_time_features
    )

    logger.info(f"数据集大小: {len(dataset)}")

    return dataset

def create_model(tokenizer, args):
    """创建模型"""
    logger.info("创建模型...")

    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=tokenizer.vocab_size,
        seq_len=args.seq_len,
        n_layer=args.n_layer,
        n_head=args.n_head,
        d_model=args.d_model,
        code_size=args.code_size,
        dropout=args.dropout,
        bias=False,
        use_time_features=args.use_time_features,
        n_time_features=8,  # 固定为8个时间特征
        label_smoothing=args.label_smoothing,
        use_auxiliary_loss=args.use_auxiliary_loss
    )

    # 移动到设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    logger.info(f"模型参数数量: {model.get_num_params():,}")
    logger.info(f"使用设备: {device}")

    return model, device

def create_optimizer_and_scheduler(model, train_loader, args):
    """创建优化器和学习率调度器"""
    logger.info("创建优化器和学习率调度器...")

    # 创建优化器
    optimizer = AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=args.weight_decay,
        betas=(0.9, 0.95)
    )

    # 计算总步数
    # 添加足够的缓冲区，确保不会超出步数限制
    # 计算实际的更新步数，考虑梯度累积
    updates_per_epoch = (len(train_loader) + args.grad_accum_steps - 1) // args.grad_accum_steps
    total_steps = updates_per_epoch * args.epochs + 10  # 添加额外的缓冲区

    # 创建学习率调度器
    scheduler = OneCycleLR(
        optimizer,
        max_lr=args.learning_rate,
        total_steps=total_steps,
        pct_start=args.warmup_ratio,
        anneal_strategy='cos',
        div_factor=25.0,
        final_div_factor=10000.0
    )

    return optimizer, scheduler

def train_epoch(model, train_loader, optimizer, scheduler, scaler, device, args):
    """训练一个epoch"""
    model.train()
    epoch_loss = 0
    epoch_samples = 0

    # 初始化进度条
    progress_bar = tqdm(train_loader, desc="Training")

    for batch_idx, batch in enumerate(progress_bar):
        # 解包批次数据
        if len(batch) == 4:
            input_tokens, target_tokens, code_ids, time_features = batch
            input_tokens = input_tokens.to(device)
            target_tokens = target_tokens.to(device)
            code_ids = code_ids.to(device)
            if time_features is not None:
                time_features = time_features.to(device)
        else:
            input_tokens, target_tokens, code_ids = batch
            input_tokens = input_tokens.to(device)
            target_tokens = target_tokens.to(device)
            code_ids = code_ids.to(device)
            time_features = None

        # 混合精度训练
        with torch.cuda.amp.autocast(enabled=args.mixed_precision):
            # 前向传播
            _, loss = model(input_tokens, code_ids, time_features, target_tokens)

        # 反向传播
        scaler.scale(loss).backward()

        # 梯度累积
        if (batch_idx + 1) % args.grad_accum_steps == 0 or (batch_idx + 1) == len(train_loader):
            # 梯度裁剪
            if args.grad_clip > 0:
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)

            # 更新参数
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad(set_to_none=True)

            # 更新学习率
            scheduler.step()

        # 累加损失
        batch_size = input_tokens.size(0)
        epoch_loss += loss.item() * batch_size
        epoch_samples += batch_size

        # 更新进度条
        progress_bar.set_postfix({
            'loss': loss.item(),
            'lr': scheduler.get_last_lr()[0]
        })

    # 计算平均损失
    avg_loss = epoch_loss / epoch_samples

    return avg_loss

def evaluate(model, val_loader, device, args):
    """评估模型"""
    model.eval()
    total_loss = 0
    total_samples = 0

    with torch.no_grad():
        for batch in tqdm(val_loader, desc="Evaluating"):
            # 解包批次数据
            if len(batch) == 4:
                input_tokens, target_tokens, code_ids, time_features = batch
                input_tokens = input_tokens.to(device)
                target_tokens = target_tokens.to(device)
                code_ids = code_ids.to(device)
                if time_features is not None:
                    time_features = time_features.to(device)
            else:
                input_tokens, target_tokens, code_ids = batch
                input_tokens = input_tokens.to(device)
                target_tokens = target_tokens.to(device)
                code_ids = code_ids.to(device)
                time_features = None

            # 前向传播
            _, loss = model(input_tokens, code_ids, time_features, target_tokens)

            # 累加损失
            batch_size = input_tokens.size(0)
            total_loss += loss.item() * batch_size
            total_samples += batch_size

    # 计算平均损失
    avg_loss = total_loss / total_samples
    logger.info(f"验证损失: {avg_loss:.4f}")
    return avg_loss

def save_checkpoint(model, optimizer, scheduler, step, loss, args, fold=None, is_best=False, suffix=None):
    """保存检查点"""
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'step': step,
        'loss': loss,
        'config': vars(args)
    }

    # 创建文件名
    if fold is not None:
        prefix = f"fold_{fold}_"
    else:
        prefix = ""

    if is_best:
        checkpoint_path = os.path.join(args.save_dir, f"{prefix}best_model.pt")
    elif suffix:
        checkpoint_path = os.path.join(args.save_dir, f"{prefix}checkpoint_{suffix}.pt")
    else:
        checkpoint_path = os.path.join(args.save_dir, f"{prefix}checkpoint_step_{step}.pt")

    torch.save(checkpoint, checkpoint_path)
    logger.info(f"模型保存到 {checkpoint_path}")

def plot_history(history, args, fold=None):
    """绘制训练历史"""
    plt.figure(figsize=(12, 4))

    # 绘制损失
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.title('训练和验证损失')
    plt.legend()

    # 绘制学习率
    plt.subplot(1, 2, 2)
    plt.plot(history['lr'])
    plt.xlabel('Epoch')
    plt.ylabel('学习率')
    plt.title('学习率变化')

    plt.tight_layout()

    # 创建文件名
    if fold is not None:
        prefix = f"fold_{fold}_"
    else:
        prefix = ""

    plt.savefig(os.path.join(args.save_dir, f"{prefix}training_history.png"))
    plt.close()

def export_model_to_onnx(model_path, tokenizer, args, device='cpu'):
    """
    将模型导出为ONNX格式

    Args:
        model_path: 模型路径
        tokenizer: 分词器
        args: 参数
        device: 设备
    """
    logger.info(f"正在将模型 {model_path} 导出为ONNX格式...")

    # 加载模型
    checkpoint = torch.load(model_path, map_location=device)

    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=tokenizer.vocab_size,
        code_size=args.code_size,
        seq_len=args.seq_len,
        n_layer=args.n_layer,
        n_head=args.n_head,
        d_model=args.d_model,
        dropout=0.0,  # 推理时不需要dropout
        bias=False,
        use_time_features=args.use_time_features,
        n_time_features=8,  # 固定为8个时间特征
        label_smoothing=args.label_smoothing,
        use_auxiliary_loss=args.use_auxiliary_loss
    )

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model = model.to(device)

    # 导出为ONNX
    onnx_path = os.path.splitext(model_path)[0] + '.onnx'

    # 设置输入形状
    input_shape = (1, args.seq_len)
    code_shape = (1,)
    time_shape = (1, args.seq_len, 8) if args.use_time_features else None

    # 导出模型
    try:
        model.to_onnx(
            onnx_path,
            input_shape=input_shape,
            code_shape=code_shape,
            time_shape=time_shape
        )
        logger.info(f"模型已成功导出为ONNX格式: {onnx_path}")
        return onnx_path
    except Exception as e:
        logger.error(f"导出ONNX模型时出错: {e}")
        return None

def cross_validate(data, code_ids, tokenizer, args):
    """交叉验证训练"""
    logger.info(f"开始 {args.num_folds} 折交叉验证...")

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 创建完整数据集
    full_dataset = create_datasets(data, code_ids, tokenizer, args)

    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)

    # K-Fold交叉验证器
    kf = KFold(n_splits=args.num_folds, shuffle=True, random_state=args.seed)

    # 获取所有样本的索引
    indices = np.arange(len(full_dataset))

    # 存储每折的结果
    fold_results = []

    # 遍历每一折
    for fold, (train_indices, val_indices) in enumerate(kf.split(indices)):
        logger.info(f"\n--- Fold {fold + 1}/{args.num_folds} ---")

        # 创建当前fold的数据子集
        train_subset = Subset(full_dataset, train_indices)
        val_subset = Subset(full_dataset, val_indices)

        # 创建数据加载器
        train_loader = DataLoader(
            train_subset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=args.num_workers,
            pin_memory=True
        )

        val_loader = DataLoader(
            val_subset,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=args.num_workers,
            pin_memory=True
        )

        logger.info(f"Fold {fold+1}: 训练集大小: {len(train_subset)}, 验证集大小: {len(val_subset)}")

        # 创建模型
        model, device = create_model(tokenizer, args)

        # 创建优化器和学习率调度器
        optimizer, scheduler = create_optimizer_and_scheduler(model, train_loader, args)

        # 初始化混合精度训练
        scaler = GradScaler(enabled=args.mixed_precision)

        # 初始化训练状态
        best_val_loss = float('inf')
        no_improve_count = 0
        global_step = 0

        # 记录训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'lr': []
        }

        # 训练循环
        for epoch in range(args.epochs):
            logger.info(f"Fold {fold+1}, Epoch {epoch+1}/{args.epochs}")

            # 训练一个epoch
            train_loss = train_epoch(model, train_loader, optimizer, scheduler, scaler, device, args)

            # 评估
            val_loss = evaluate(model, val_loader, device, args)

            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['lr'].append(scheduler.get_last_lr()[0])

            logger.info(f"Fold {fold+1}, Epoch {epoch+1}: 训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}")

            # 更新全局步数
            global_step += len(train_loader)

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                no_improve_count = 0
                save_checkpoint(model, optimizer, scheduler, global_step, val_loss, args, fold=fold+1, is_best=True)
                logger.info(f"发现更好的模型! 验证损失: {val_loss:.4f}")
            else:
                no_improve_count += 1

            # 早停
            if args.early_stopping > 0 and no_improve_count >= args.early_stopping:
                logger.info(f"早停! {args.early_stopping} 个epoch后验证损失没有改善")
                break

            # 保存每个epoch的模型
            save_checkpoint(model, optimizer, scheduler, global_step, train_loss, args, fold=fold+1, suffix=f"epoch_{epoch+1}")

        # 绘制训练历史
        plot_history(history, args, fold=fold+1)

        # 存储结果
        fold_results.append({
            'fold': fold + 1,
            'best_val_loss': best_val_loss,
            'history': history
        })

        # 清理GPU内存
        del model, optimizer, scheduler, scaler
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    # 汇总结果
    logger.info("\n--- 交叉验证汇总 ---")
    all_best_val_losses = [res['best_val_loss'] for res in fold_results]

    for i, res in enumerate(fold_results):
        logger.info(f"Fold {res['fold']}: 最佳验证损失 = {res['best_val_loss']:.4f}")

    avg_best_val_loss = np.mean(all_best_val_losses)
    std_best_val_loss = np.std(all_best_val_losses)

    logger.info(f"\n平均最佳验证损失: {avg_best_val_loss:.4f} ± {std_best_val_loss:.4f}")

    # 保存汇总结果
    summary = {
        'fold_results': fold_results,
        'avg_best_val_loss': float(avg_best_val_loss),
        'std_best_val_loss': float(std_best_val_loss)
    }

    with open(os.path.join(args.save_dir, 'cv_summary.json'), 'w') as f:
        json.dump(summary, f, indent=4, default=lambda x: x.tolist() if isinstance(x, np.ndarray) else x)

    # 找到最佳模型并导出为ONNX格式
    best_fold = np.argmin(all_best_val_losses)
    best_fold_idx = best_fold + 1  # 折索引从1开始
    best_model_path = os.path.join(args.save_dir, f"fold_{best_fold_idx}_best_model.pt")

    # if os.path.exists(best_model_path):
    #     logger.info(f"找到最佳模型: {best_model_path}")
    #     # 导出为ONNX格式
    #     device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    #     onnx_path = export_model_to_onnx(best_model_path, tokenizer, args, device)

    #     if onnx_path:
    #         logger.info(f"最佳模型已导出为ONNX格式: {onnx_path}")
    #     else:
    #         logger.error("导出ONNX模型失败")
    # else:
    #     logger.error(f"未找到最佳模型: {best_model_path}")

    output_name = f"vqgpt_{args.vectorization_method}_{args.market}_{args.block_name}_{args.period}_{args.seq_len}_{args.n_layer}_{args.n_head}_{args.d_model}_{args.num_embeddings}_{avg_best_val_loss:.4f}_ls"
    # 保存配置
    with open(os.path.join(args.save_dir, f'{output_name}.json'), 'w') as f:
        json.dump(vars(args), f, indent=4)

    # 导出整体最佳模型
    ensemble_best_model_path = os.path.join(args.save_dir, f"{output_name}.pt")

    # 创建整体最佳模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    ensemble_model = CandlestickVQGPT(
        vocab_size=tokenizer.vocab_size,
        code_size=args.code_size,
        seq_len=args.seq_len,
        n_layer=args.n_layer,
        n_head=args.n_head,
        d_model=args.d_model,
        dropout=0.0,  # 推理时不需要dropout
        bias=False,
        use_time_features=args.use_time_features,
        n_time_features=8,  # 固定为8个时间特征
        label_smoothing=args.label_smoothing,
        use_auxiliary_loss=args.use_auxiliary_loss
    )

    # 加载最佳模型权重
    if os.path.exists(best_model_path):
        checkpoint = torch.load(best_model_path, map_location=device)
        ensemble_model.load_state_dict(checkpoint['model_state_dict'])
        ensemble_model.eval()

        # 保存整体最佳模型
        torch.save({
            'model_state_dict': ensemble_model.state_dict(),
            'config': vars(args),
            'best_fold': best_fold_idx,
            'best_val_loss': all_best_val_losses[best_fold]
        }, ensemble_best_model_path)

        # 导出整体最佳模型为ONNX格式
        ensemble_onnx_path = export_model_to_onnx(
            ensemble_best_model_path,
            tokenizer,
            args,
            device
        )

        if ensemble_onnx_path:
            logger.info(f"整体最佳模型已导出为ONNX格式: {ensemble_onnx_path}")
        else:
            logger.error("导出整体最佳模型ONNX失败")

    return fold_results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='使用交叉验证训练CandlestickVQGPT模型')

    # 数据参数
    parser.add_argument('--data_dir', type=str, required=True, help='数据目录')
    parser.add_argument('--market', type=str, default='fut', choices=['fut', 'stk'], help='市场类型')
    parser.add_argument('--block_name', type=str, default='sf', help='市场块名称')
    parser.add_argument('--period', type=str, default='min1', choices=['day', 'min5', 'min1'], help='数据周期')
    parser.add_argument('--multi_data', action='store_true', help='是否使用多个数据文件')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')
    parser.add_argument('--stride', type=int, default=1, help='滑动窗口步长')

    # 交叉验证参数
    parser.add_argument('--num_folds', type=int, default=5, help='交叉验证折数')

    # Tokenizer参数
    parser.add_argument('--codebook_path', type=str, default=None, help='码本权重路径')
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=4, help='嵌入维度')
    parser.add_argument('--atr_period', type=int, default=14, help='ATR周期')
    parser.add_argument('--ma_volume_period', type=int, default=20, help='成交量移动平均周期')
    parser.add_argument('--vectorization_method', type=str, default='atr_based',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    parser.add_argument('--use_code_dim', action='store_true', help='是否使用证券代码维度')
    parser.add_argument('--code_dim', type=int, default=5, help='证券代码嵌入维度')

    # 模型参数
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--code_size', type=int, default=100, help='证券代码数量')
    parser.add_argument('--n_layer', type=int, default=4, help='Transformer层数')
    parser.add_argument('--n_head', type=int, default=8, help='注意力头数')
    parser.add_argument('--d_model', type=int, default=128, help='模型维度')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比例')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    parser.add_argument('--label_smoothing', type=float, default=0.1, help='标签平滑系数')
    parser.add_argument('--use_auxiliary_loss', action='store_true', help='是否使用辅助损失')

    # 训练参数
    parser.add_argument('--batch_size', type=int, default=64, help='批次大小')
    parser.add_argument('--epochs', type=int, default=5, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=1e-3, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='权重衰减')
    parser.add_argument('--warmup_ratio', type=float, default=0.1, help='预热比例')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪')
    parser.add_argument('--grad_accum_steps', type=int, default=1, help='梯度累积步数')
    parser.add_argument('--early_stopping', type=int, default=5, help='早停轮数')
    parser.add_argument('--mixed_precision', action='store_true', help='是否使用混合精度训练')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器工作进程数')

    # 其他参数
    parser.add_argument('--save_dir', type=str, default='./checkpoints/candlestick_vq_gpt_cv', help='保存目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    # 加载数据
    data_files = get_data_files(args.data_dir, args.market, args.block_name, args.period)
    if len(data_files) == 0:
        raise ValueError(f"未找到数据文件: {args.data_dir}")
    data, code_ids = load_single_data(
        data_files[0],
        begin_date=args.begin_date,
        end_date=args.end_date
    )

    logger.info(f"加载了 {len(data)} 个证券的数据")
    # 创建tokenizer
    tokenizer = create_tokenizer(args)

    # 运行交叉验证
    cross_validate(data, code_ids, tokenizer, args)

    logger.info("交叉验证完成!")

if __name__ == "__main__":
    main()
