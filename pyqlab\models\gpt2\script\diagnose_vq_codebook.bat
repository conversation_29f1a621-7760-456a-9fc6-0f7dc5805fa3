@echo off
REM Diagnose VQ Codebook Issues

REM Set default parameters
set DATA_PATH=f:/hqdata/fut_top_min1.parquet
@REM set DATA_PATH=f:/hqdata/fut_sf_min5.parquet
set CODEBOOK_PATH=e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250525\vqcb_atr_based_fut_top_min1_512_0.0209.pt
set ENCODER_ONNX_PATH= e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250525\vqcb_atr_based_fut_top_min1_512_0.0209_encoder.onnx
set DECODER_ONNX_PATH= e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250525\vqcb_atr_based_fut_top_min1_512_0.0209_decoder.onnx
set OUTPUT_DIR=E:/lab/RoboQuant/pylab/models/vqvae/diagnosis
set NUM_EMBEDDINGS=512
set EMBEDDING_DIM=4
set ATR_PERIOD=14
set MA_VOLUME_PERIOD=14
set VECTORIZATION_METHOD=atr_based
set NUM_CANDLES=50

REM Create output directory
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Run diagnosis script
python -m pyqlab.models.gpt2.diagnose_vq_codebook --data_path="%DATA_PATH%" --codebook_path="%CODEBOOK_PATH%" --encoder_onnx_path="%ENCODER_ONNX_PATH%" --decoder_onnx_path="%DECODER_ONNX_PATH%" --output_dir="%OUTPUT_DIR%" --num_embeddings=%NUM_EMBEDDINGS% --embedding_dim=%EMBEDDING_DIM% --atr_period=%ATR_PERIOD% --ma_volume_period=%MA_VOLUME_PERIOD% --vectorization_method=%VECTORIZATION_METHOD% --num_candles=%NUM_CANDLES%

REM Test different vectorization methods
echo.
echo Testing different vectorization methods...
echo.

python -m pyqlab.models.gpt2.diagnose_vq_codebook --data_path="%DATA_PATH%" --codebook_path="%CODEBOOK_PATH%" --encoder_onnx_path="%ENCODER_ONNX_PATH%" --decoder_onnx_path="%DECODER_ONNX_PATH%" --output_dir="%OUTPUT_DIR%" --num_embeddings=%NUM_EMBEDDINGS% --embedding_dim=%EMBEDDING_DIM% --atr_period=%ATR_PERIOD% --ma_volume_period=%MA_VOLUME_PERIOD% --vectorization_method=%VECTORIZATION_METHOD% --num_candles=%NUM_CANDLES%

echo.
echo Diagnosis complete! Results saved in %OUTPUT_DIR%
echo.

