"""
诊断CandlestickVQGPT模型预测问题

该脚本用于分析模型为什么总是预测相同的token，帮助诊断训练和推理问题。
"""

import os
import sys
import argparse
import json
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from collections import Counter

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.utils import load_single_data, extract_time_features


def create_tokenizer(args):
    """创建Tokenizer"""
    print("创建Tokenizer...")
    
    # 选择向量化方法
    if args.vectorization_method == 'atr_based':
        vectorization_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vectorization_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vectorization_method = VectorizationMethod.LOG_RETURN
    elif args.vectorization_method == 'zscore':
        vectorization_method = VectorizationMethod.ZSCORE
    elif args.vectorization_method == 'minmax':
        vectorization_method = VectorizationMethod.MINMAX
    else:
        raise ValueError(f"未知的向量化方法: {args.vectorization_method}")

    # 从配置文件中获取参数
    config = {}
    if args.model_path:
        config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)

    # 创建Tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=config.get('num_embeddings', 512),
        embedding_dim=config.get('embedding_dim', 4),
        atr_period=config.get('atr_period', 14),
        ma_volume_period=config.get('ma_volume_period', 14),
        vectorization_method=vectorization_method,
        detect_gaps=True,
        gap_threshold=7.0,
        use_code_dim=False,
        code_dim=16
    )

    print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")
    return tokenizer


def load_model(args, tokenizer):
    """加载模型"""
    print(f"加载模型: {args.model_path}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载检查点
    checkpoint = torch.load(args.model_path, map_location=device)
    config = checkpoint.get('config', {})
    
    # 使用tokenizer的词汇表大小
    vocab_size = tokenizer.vocab_size
    
    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=vocab_size,
        code_size=config.get('code_size', 100),
        seq_len=config.get('seq_len', config.get('block_size', 30)),
        n_layer=config.get('n_layer', 4),
        n_head=config.get('n_head', 8),
        d_model=config.get('d_model', 128),
        dropout=0.0,
        bias=False,
        use_time_features=config.get('use_time_features', True),
        n_time_features=config.get('n_time_features', 8),
        label_smoothing=0.0,
        use_auxiliary_loss=False
    )
    
    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)
    
    print(f"模型加载成功，参数数量: {model.get_num_params():,}")
    print(f"模型配置: {config}")
    
    return model, device


def analyze_model_weights(model):
    """分析模型权重"""
    print("\n=== 模型权重分析 ===")
    
    # 分析token embedding权重
    token_emb = model.token_embedding.weight.data
    print(f"Token embedding权重形状: {token_emb.shape}")
    print(f"Token embedding权重统计:")
    print(f"  最小值: {token_emb.min().item():.6f}")
    print(f"  最大值: {token_emb.max().item():.6f}")
    print(f"  平均值: {token_emb.mean().item():.6f}")
    print(f"  标准差: {token_emb.std().item():.6f}")
    
    # 分析输出层权重
    head_weight = model.head.weight.data
    print(f"\n输出层权重形状: {head_weight.shape}")
    print(f"输出层权重统计:")
    print(f"  最小值: {head_weight.min().item():.6f}")
    print(f"  最大值: {head_weight.max().item():.6f}")
    print(f"  平均值: {head_weight.mean().item():.6f}")
    print(f"  标准差: {head_weight.std().item():.6f}")
    
    # 检查权重是否有异常
    if torch.isnan(token_emb).any():
        print("警告: Token embedding权重包含NaN值!")
    if torch.isnan(head_weight).any():
        print("警告: 输出层权重包含NaN值!")
    
    return {
        'token_emb_stats': {
            'min': token_emb.min().item(),
            'max': token_emb.max().item(),
            'mean': token_emb.mean().item(),
            'std': token_emb.std().item()
        },
        'head_weight_stats': {
            'min': head_weight.min().item(),
            'max': head_weight.max().item(),
            'mean': head_weight.mean().item(),
            'std': head_weight.std().item()
        }
    }


def analyze_predictions_diversity(model, tokenizer, data_samples, device, num_samples=50):
    """分析预测多样性"""
    print(f"\n=== 预测多样性分析 (分析{num_samples}个样本) ===")
    
    all_predictions = []
    all_logits = []
    
    model.eval()
    with torch.no_grad():
        for i, (df, code_id) in enumerate(data_samples[:num_samples]):
            # Tokenize数据
            tokens = tokenizer.tokenize(df)
            if len(tokens) < 30:
                continue
                
            # 准备输入
            input_tokens = torch.tensor(tokens[-30:], dtype=torch.int32).unsqueeze(0).to(device)
            code_ids = torch.tensor([code_id], dtype=torch.int32).to(device)
            
            # 提取时间特征
            time_features = extract_time_features(df.iloc[-30:])
            time_features = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(device)
            
            # 前向传播
            logits, _ = model(input_tokens, code_ids, time_features)
            
            # 获取最后一个时间步的logits
            last_logits = logits[0, -1, :].cpu().numpy()
            all_logits.append(last_logits)
            
            # 计算概率分布
            probs = torch.softmax(logits[0, -1, :], dim=-1).cpu().numpy()
            
            # 获取top-5预测
            top_indices = np.argsort(probs)[::-1][:5]
            top_probs = probs[top_indices]
            
            all_predictions.append({
                'sample_idx': i,
                'top_indices': top_indices.tolist(),
                'top_probs': top_probs.tolist(),
                'entropy': -np.sum(probs * np.log(probs + 1e-10))
            })
            
            if i < 5:  # 打印前5个样本的详细信息
                print(f"\n样本 {i}:")
                print(f"  Top-5 indices: {top_indices}")
                print(f"  Top-5 probs: {top_probs}")
                print(f"  熵值: {all_predictions[-1]['entropy']:.4f}")
    
    # 分析预测统计
    print(f"\n=== 预测统计分析 ===")
    
    # 统计最常预测的token
    all_top1 = [pred['top_indices'][0] for pred in all_predictions]
    top1_counter = Counter(all_top1)
    print(f"最常预测的Top-1 tokens:")
    for token_id, count in top1_counter.most_common(10):
        print(f"  Token {token_id}: {count}次 ({count/len(all_predictions):.2%})")
    
    # 计算预测多样性
    unique_top1 = len(set(all_top1))
    print(f"\n预测多样性:")
    print(f"  唯一的Top-1预测数量: {unique_top1}")
    print(f"  预测多样性比例: {unique_top1/tokenizer.vocab_size:.2%}")
    
    # 计算平均熵
    avg_entropy = np.mean([pred['entropy'] for pred in all_predictions])
    max_entropy = np.log(tokenizer.vocab_size)
    print(f"  平均熵值: {avg_entropy:.4f}")
    print(f"  最大可能熵值: {max_entropy:.4f}")
    print(f"  熵值比例: {avg_entropy/max_entropy:.2%}")
    
    return all_predictions, all_logits


def analyze_input_diversity(data_samples, tokenizer, num_samples=50):
    """分析输入数据多样性"""
    print(f"\n=== 输入数据多样性分析 (分析{num_samples}个样本) ===")
    
    all_tokens = []
    
    for i, (df, code_id) in enumerate(data_samples[:num_samples]):
        tokens = tokenizer.tokenize(df)
        if len(tokens) >= 30:
            all_tokens.extend(tokens[-30:])
    
    # 统计token分布
    token_counter = Counter(all_tokens)
    print(f"输入token统计:")
    print(f"  总token数量: {len(all_tokens)}")
    print(f"  唯一token数量: {len(token_counter)}")
    print(f"  token多样性: {len(token_counter)/tokenizer.vocab_size:.2%}")
    
    print(f"\n最常见的tokens:")
    for token_id, count in token_counter.most_common(10):
        print(f"  Token {token_id}: {count}次 ({count/len(all_tokens):.2%})")
    
    return token_counter


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='诊断CandlestickVQGPT模型预测问题')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本文件路径')
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--vectorization_method', type=str, default='atr_based',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    
    # 分析参数
    parser.add_argument('--num_samples', type=int, default=50, help='分析的样本数量')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')
    
    args = parser.parse_args()
    
    # 创建tokenizer
    tokenizer = create_tokenizer(args)
    
    # 加载模型
    model, device = load_model(args, tokenizer)
    
    # 加载数据
    print(f"\n加载数据: {args.data_path}")
    data, code_ids = load_single_data(
        args.data_path,
        begin_date=args.begin_date,
        end_date=args.end_date
    )
    
    if len(data) == 0:
        print("错误: 未找到符合条件的数据")
        return
    
    print(f"加载了 {len(data)} 个数据文件")
    
    # 准备数据样本
    data_samples = []
    for i, df in enumerate(data):
        if len(df) >= 50:  # 确保有足够的数据
            data_samples.append((df, code_ids[i]))
    
    print(f"有效数据样本: {len(data_samples)}")
    
    # 1. 分析模型权重
    weight_stats = analyze_model_weights(model)
    
    # 2. 分析输入数据多样性
    input_stats = analyze_input_diversity(data_samples, tokenizer, args.num_samples)
    
    # 3. 分析预测多样性
    pred_stats, all_logits = analyze_predictions_diversity(model, tokenizer, data_samples, device, args.num_samples)
    
    # 4. 生成诊断报告
    print(f"\n=== 诊断总结 ===")
    
    # 检查是否存在问题
    issues = []
    
    # 检查预测多样性
    unique_predictions = len(set(pred['top_indices'][0] for pred in pred_stats))
    if unique_predictions < tokenizer.vocab_size * 0.1:
        issues.append(f"预测多样性过低: 只有{unique_predictions}个不同的预测")
    
    # 检查熵值
    avg_entropy = np.mean([pred['entropy'] for pred in pred_stats])
    max_entropy = np.log(tokenizer.vocab_size)
    if avg_entropy < max_entropy * 0.3:
        issues.append(f"预测熵值过低: {avg_entropy:.4f} < {max_entropy*0.3:.4f}")
    
    # 检查输入多样性
    if len(input_stats) < tokenizer.vocab_size * 0.5:
        issues.append(f"输入token多样性不足: 只使用了{len(input_stats)}个不同的token")
    
    if issues:
        print("发现的问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        print("\n建议的解决方案:")
        print("  1. 检查训练数据的多样性，确保包含各种市场情况")
        print("  2. 增加训练轮数或调整学习率")
        print("  3. 检查模型架构是否合适")
        print("  4. 尝试不同的温度参数进行推理")
        print("  5. 检查tokenizer的码本是否训练充分")
    else:
        print("未发现明显问题，模型预测看起来正常")


if __name__ == "__main__":
    main()
