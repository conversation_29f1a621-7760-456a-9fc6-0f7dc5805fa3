"""
多证券回测CandlestickVQGPT模型

该脚本用于对训练好的CandlestickVQGPT模型进行多证券回测，评估其在实际交易中的表现。
支持加载ONNX格式的模型和码本。
"""

import os
import sys
import argparse
import json
import time
import logging
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入模型和回测器
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.backtester import CandlestickLLMBacktester
from pyqlab.models.gpt2.utils import load_single_data, extract_time_features

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("backtest_mult_candlestick_vq_gpt.log")
    ]
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='多证券回测CandlestickVQGPT模型')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')
    parser.add_argument('--max_codes', type=int, default=5, help='最大证券代码数量')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本文件路径')
    parser.add_argument('--vectorization_method', type=str, default='percent_change',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    
    # 回测参数
    parser.add_argument('--initial_capital', type=float, default=10000.0, help='初始资金')
    parser.add_argument('--commission', type=float, default=0.001, help='交易手续费率')
    parser.add_argument('--threshold', type=float, default=0.6, help='交易信号阈值')
    parser.add_argument('--stop_loss', type=float, default=None, help='止损比例')
    parser.add_argument('--take_profit', type=float, default=None, help='止盈比例')
    parser.add_argument('--temperature', type=float, default=0.8, help='温度参数')
    parser.add_argument('--top_k', type=int, default=50, help='Top-K采样参数')
    parser.add_argument('--signal_type', type=str, default='topk',
                        choices=['threshold', 'topk', 'momentum', 'ensemble'],
                        help='信号生成器类型')
    parser.add_argument('--leverage', type=float, default=1.0, help='杠杆倍数')
    parser.add_argument('--print_interval', type=int, default=10, help='打印间隔')
    parser.add_argument('--equal_weight', action='store_true', help='是否使用等权重分配资金')
    
    # 其他参数
    parser.add_argument('--output_dir', type=str, default='./results', help='输出目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def create_tokenizer(args):
    """创建Tokenizer"""
    logger.info("创建Tokenizer...")
    
    # 选择向量化方法
    if args.vectorization_method == 'atr_based':
        vectorization_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vectorization_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vectorization_method = VectorizationMethod.LOG_RETURN
    elif args.vectorization_method == 'zscore':
        vectorization_method = VectorizationMethod.ZSCORE
    elif args.vectorization_method == 'minmax':
        vectorization_method = VectorizationMethod.MINMAX
    else:
        raise ValueError(f"未知的向量化方法: {args.vectorization_method}")
    
    # 从配置文件中获取参数
    config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {}
    
    # 创建Tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=config.get('num_embeddings', 512),
        embedding_dim=config.get('embedding_dim', 5),
        atr_period=config.get('atr_period', 100),
        ma_volume_period=config.get('ma_volume_period', 100),
        vectorization_method=vectorization_method,
        detect_gaps=True,
        gap_threshold=2.0
    )
    
    logger.info(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")
    
    return tokenizer


def load_model(args, tokenizer):
    """加载模型"""
    logger.info(f"加载模型: {args.model_path}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 检查是否是ONNX模型
    if args.model_path.endswith('.onnx'):
        # 使用ONNX运行时加载模型
        try:
            import onnxruntime as ort
            logger.info("使用ONNX运行时加载模型")
            # 创建一个包装类来模拟PyTorch模型接口
            class ONNXModelWrapper:
                def __init__(self, onnx_path, device):
                    self.onnx_path = onnx_path
                    self.device = device
                    # 创建ONNX运行时会话
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if device.type == 'cuda' else ['CPUExecutionProvider']
                    self.session = ort.InferenceSession(onnx_path, providers=providers)
                    # 获取输入和输出名称
                    self.input_names = [input.name for input in self.session.get_inputs()]
                    self.output_names = [output.name for output in self.session.get_outputs()]
                
                def to(self, device):
                    # 不需要实际操作，只是为了兼容PyTorch接口
                    return self
                
                def eval(self):
                    # 不需要实际操作，只是为了兼容PyTorch接口
                    return self
                
                def generate(self, input_tokens, code_ids, time_features=None, max_new_tokens=10, temperature=1.0, top_k=None):
                    # 准备输入
                    inputs = {
                        'input_tokens': input_tokens.cpu().numpy(),
                        'code_ids': code_ids.cpu().numpy()
                    }
                    if time_features is not None and 'time_features' in self.input_names:
                        inputs['time_features'] = time_features.cpu().numpy()
                    
                    # 运行推理
                    outputs = self.session.run(self.output_names, inputs)
                    
                    # 将输出转换为PyTorch张量
                    logits = torch.tensor(outputs[0], device=self.device)
                    
                    # 生成新的token
                    batch_size, seq_len = input_tokens.size()
                    tokens = input_tokens.clone()
                    
                    for _ in range(max_new_tokens):
                        # 获取最后一个时间步的logits
                        last_logits = logits[:, -1, :] / (temperature if temperature > 0 else 1.0)
                        
                        # 可选的top-k采样
                        if top_k is not None:
                            v, _ = torch.topk(last_logits, min(top_k, last_logits.size(-1)))
                            last_logits[last_logits < v[:, [-1]]] = -float('Inf')
                        
                        # 应用softmax获取概率分布
                        probs = torch.nn.functional.softmax(last_logits, dim=-1)
                        
                        # 采样下一个token
                        next_token = torch.multinomial(probs, num_samples=1)
                        
                        # 将新token添加到序列中
                        tokens = torch.cat([tokens, next_token], dim=1)
                        
                        # 准备下一次推理的输入
                        inputs = {
                            'input_tokens': tokens.cpu().numpy(),
                            'code_ids': code_ids.cpu().numpy()
                        }
                        if time_features is not None and 'time_features' in self.input_names:
                            # 扩展时间特征
                            new_time_features = torch.zeros(batch_size, 1, time_features.size(2), device=time_features.device)
                            time_features = torch.cat([time_features, new_time_features], dim=1)
                            inputs['time_features'] = time_features.cpu().numpy()
                        
                        # 运行推理
                        outputs = self.session.run(self.output_names, inputs)
                        logits = torch.tensor(outputs[0], device=self.device)
                    
                    return tokens
            
            # 创建ONNX模型包装器
            model = ONNXModelWrapper(args.model_path, device)
            
        except ImportError:
            logger.error("未安装onnxruntime，无法加载ONNX模型")
            raise
    else:
        # 加载PyTorch模型
        logger.info("加载PyTorch模型")
        
        # 从检查点中获取模型配置
        config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            config = {}
        
        # 创建模型
        model = CandlestickVQGPT.from_pretrained(args.model_path, device=device)
        model.eval()
    
    logger.info(f"使用设备: {device}")
    
    return model, device


def backtest_multiple(backtester, data_dict, code_ids, args):
    """对多个证券代码进行回测"""
    logger.info(f"开始对 {len(data_dict)} 个证券代码进行回测...")
    
    # 创建结果字典
    results_dict = {}
    summary = {
        'total_return': 0.0,
        'win_rate': 0.0,
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'max_drawdown': 0.0,
        'sharpe_ratio': 0.0,
        'equity_curves': {}
    }
    
    # 计算每个证券的资金分配
    if args.equal_weight:
        allocation = {code_id: 1.0 / len(code_ids) for code_id in code_ids}
    else:
        # 根据数据量分配资金
        total_samples = sum(len(df) for df in data_dict.values())
        allocation = {code_id: len(data_dict[code_id]) / total_samples for code_id in code_ids}
    
    # 对每个证券进行回测
    for i, code_id in enumerate(code_ids):
        df = data_dict[code_id]
        logger.info(f"回测证券 {i+1}/{len(code_ids)}: 代码 {code_id}, 数据量 {len(df)}, 资金分配 {allocation[code_id]:.2%}")
        
        # 提取时间特征
        time_features = extract_time_features(df) if args.use_time_features else None
        
        # 计算初始资金
        initial_capital = args.initial_capital * allocation[code_id]
        
        # 进行回测
        results = backtester.backtest(
            df=df,
            code_id=code_id,
            seq_len=args.seq_len,
            commission=args.commission,
            threshold=args.threshold,
            stop_loss=args.stop_loss,
            take_profit=args.take_profit,
            time_features=time_features,
            temperature=args.temperature,
            print_interval=args.print_interval
        )
        
        # 保存结果
        results_dict[code_id] = results
        
        # 更新汇总信息
        summary['total_return'] += results['total_return'] * allocation[code_id]
        summary['total_trades'] += results['total_trades']
        summary['winning_trades'] += results['winning_trades']
        summary['losing_trades'] += results['losing_trades']
        summary['equity_curves'][code_id] = results['equity_curve']
        
        # 可视化回测结果
        backtester.visualize_backtest(
            df=df,
            results=results,
            seq_len=args.seq_len,
            save_path=os.path.join(args.output_dir, f'backtest_chart_code_{code_id}.png')
        )
    
    # 计算整体胜率
    if summary['total_trades'] > 0:
        summary['win_rate'] = summary['winning_trades'] / summary['total_trades']
    
    # 计算整体最大回撤和夏普比率
    # 这里简化处理，实际应该基于组合的权益曲线计算
    summary['max_drawdown'] = max([results['max_drawdown'] for results in results_dict.values()])
    summary['sharpe_ratio'] = np.mean([results['sharpe_ratio'] for results in results_dict.values() if results['sharpe_ratio'] is not None])
    
    # 绘制组合权益曲线
    plt.figure(figsize=(12, 6))
    for code_id, equity_curve in summary['equity_curves'].items():
        plt.plot(equity_curve, label=f'代码 {code_id}')
    plt.title('多证券回测权益曲线')
    plt.xlabel('交易次数')
    plt.ylabel('权益')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(args.output_dir, 'portfolio_equity_curve.png'))
    plt.close()
    
    # 保存汇总结果
    with open(os.path.join(args.output_dir, 'portfolio_summary.json'), 'w') as f:
        # 将numpy数组转换为列表
        summary_json = {k: v if not isinstance(v, np.ndarray) else v.tolist() 
                        for k, v in summary.items() if k != 'equity_curves'}
        json.dump(summary_json, f, indent=4)
    
    return results_dict, summary


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    logger.info(f"加载数据: {args.data_path}")
    data, code_ids = load_single_data(
        args.data_path,
        begin_date=args.begin_date,
        end_date=args.end_date
    )
    
    if len(data) == 0:
        logger.error("未找到符合条件的数据")
        return
    
    # 限制证券代码数量
    if args.max_codes > 0 and len(data) > args.max_codes:
        logger.info(f"限制证券代码数量为 {args.max_codes}")
        data = data[:args.max_codes]
        code_ids = code_ids[:args.max_codes]
    
    # 创建数据字典
    data_dict = {code_id: df for code_id, df in zip(code_ids, data)}
    
    # 创建tokenizer
    tokenizer = create_tokenizer(args)
    
    # 加载模型
    model, device = load_model(args, tokenizer)
    
    # 创建回测器
    logger.info("创建回测器...")
    backtester = CandlestickLLMBacktester(
        model=model,
        tokenizer=tokenizer,
        initial_capital=args.initial_capital,
        device=device,
        signal_type=args.signal_type,
        leverage=args.leverage
    )
    
    # 进行多证券回测
    results_dict, summary = backtest_multiple(backtester, data_dict, code_ids, args)
    
    # 输出汇总结果
    logger.info("\n回测汇总结果:")
    logger.info(f"总收益率: {summary['total_return']:.2%}")
    logger.info(f"总交易次数: {summary['total_trades']}")
    logger.info(f"胜率: {summary['win_rate']:.2%}")
    logger.info(f"最大回撤: {summary['max_drawdown']:.2%}")
    logger.info(f"夏普比率: {summary['sharpe_ratio']:.2f}")
    
    logger.info(f"\n回测完成，结果已保存到 {args.output_dir}")


if __name__ == "__main__":
    main()
