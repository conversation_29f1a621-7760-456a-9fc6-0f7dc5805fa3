"""
测试CandlestickVQGPT模型的脚本
"""

import os
import json
import argparse
import logging
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm

# 导入自定义模块
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.utils import load_single_data

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("test_candlestick_vq_gpt.log")
    ]
)
logger = logging.getLogger(__name__)

def load_model_and_tokenizer(args):
    """加载模型和tokenizer"""
    logger.info(f"加载模型: {args.model_path}")

    # 加载模型检查点
    checkpoint = torch.load(args.model_path, map_location='cpu')
    config = checkpoint.get('config', {})

    # 创建tokenizer
    if isinstance(config, dict) and 'vectorization_method' in config:
        vectorization_method = config['vectorization_method']
    else:
        vectorization_method = args.vectorization_method

    if vectorization_method == 'atr_based':
        vector_method = VectorizationMethod.ATR_BASED
    elif vectorization_method == 'percent_change':
        vector_method = VectorizationMethod.PERCENT_CHANGE
    elif vectorization_method == 'log_return':
        vector_method = VectorizationMethod.LOG_RETURN
    elif vectorization_method == 'zscore':
        vector_method = VectorizationMethod.ZSCORE
    elif vectorization_method == 'minmax':
        vector_method = VectorizationMethod.MINMAX
    else:
        raise ValueError(f"Unknown vectorization method: {vectorization_method}")

    # 创建tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=config.get('num_embeddings', args.num_embeddings),
        embedding_dim=config.get('embedding_dim', args.embedding_dim),
        atr_period=config.get('atr_period', args.atr_period),
        ma_volume_period=config.get('ma_volume_period', args.ma_volume_period),
        vectorization_method=vector_method
    )

    # 从检查点中获取模型配置
    config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)

    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=tokenizer.vocab_size,
        code_size=config.get('code_size', args.code_size),
        seq_len=config.get('block_size', args.seq_len),
        n_layer=config.get('n_layer', 4),
        n_head=config.get('n_head', 4),
        d_model=config.get('d_model', 128),
        dropout=config.get('dropout', 0.1),
        use_time_features=config.get('use_time_features', args.use_time_features),
        n_time_features=8,  # 固定为8个时间特征
        label_smoothing=config.get('label_smoothing', 0.1),
        use_auxiliary_loss=config.get('use_auxiliary_loss', True)
    )

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])

    # 移动到设备
    device = torch.device('cuda' if torch.cuda.is_available() and not args.cpu else 'cpu')
    model = model.to(device)
    model.eval()

    logger.info(f"模型参数数量: {model.get_num_params():,}")
    logger.info(f"使用设备: {device}")

    return model, tokenizer, device

def extract_time_features(df):
    """提取时间特征"""
    if 'datetime' not in df.columns:
        return None

    # 将datetime转换为pandas datetime
    if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
        df['datetime'] = pd.to_datetime(df['datetime'])

    # 提取时间特征
    features = []
    for dt in df['datetime']:
        # 小时 (0-23)
        hour = dt.hour / 23.0 - 0.5
        # 星期几 (0-6)
        day_of_week = dt.dayofweek / 6.0 - 0.5
        # 月份中的日期 (1-31)
        day_of_month = (dt.day - 1) / 30.0 - 0.5
        # 年份中的日期 (1-366)
        day_of_year = (dt.dayofyear - 1) / 365.0 - 0.5
        # 月份 (1-12)
        month = (dt.month - 1) / 11.0 - 0.5

        features.append([hour, day_of_week, day_of_month, day_of_year, month])

    return np.array(features, dtype=np.float32)

def predict_next_candles(model, tokenizer, df, code_id, device, args):
    """预测未来的K线"""
    # 预处理数据
    processed_df = tokenizer._preprocess_df(df)

    # 生成token序列
    token_ids = []
    for i in range(len(processed_df)):
        row = processed_df.iloc[i]
        # 检查是否有足够的历史数据
        if pd.isna(row['ATR']) or pd.isna(row['MA_Volume']) or pd.isna(row['Prev_Close']):
            token_ids.append(tokenizer.unk_token_id)
            continue

        token_id = tokenizer.tokenize_single_candlestick(
            row,
            row['Prev_Close'],
            row['MA_Volume'],
            row['ATR']
        )
        token_ids.append(token_id)

    # 提取时间特征
    time_features = extract_time_features(df) if args.use_time_features else None

    # 准备输入序列
    input_seq = token_ids[-args.seq_len:]
    input_tensor = torch.tensor(input_seq, dtype=torch.long).unsqueeze(0).to(device)
    code_id_tensor = torch.tensor([code_id], dtype=torch.long).to(device)

    # 准备时间特征
    if time_features is not None:
        time_features_tensor = torch.tensor(time_features[-args.seq_len:], dtype=torch.float).unsqueeze(0).to(device)
    else:
        time_features_tensor = None

    # 生成预测
    with torch.no_grad():
        generated_tokens = model.generate(
            input_tensor,
            code_id_tensor,
            time_features_tensor,
            max_new_tokens=args.pred_len,
            temperature=args.temperature,
            top_k=args.top_k
        )

    # 提取预测的token
    predicted_tokens = generated_tokens[0, -args.pred_len:].cpu().numpy().tolist()

    # 将token转换回K线数据
    predicted_candles = tokenizer.tokens_to_candlesticks(predicted_tokens, df)

    return predicted_candles

def plot_predictions(original_df, predicted_df, args):
    """绘制预测结果"""
    plt.figure(figsize=(12, 6))

    # 绘制原始K线
    ax1 = plt.subplot(2, 1, 1)
    plot_candlesticks(ax1, original_df.iloc[-args.seq_len:], title="原始K线")

    # 绘制预测K线
    ax2 = plt.subplot(2, 1, 2)
    plot_candlesticks(ax2, predicted_df, title="预测K线")

    plt.tight_layout()
    plt.savefig(args.output_file)
    plt.close()

    logger.info(f"预测结果已保存到 {args.output_file}")

def plot_candlesticks(ax, df, title=None):
    """绘制K线图"""
    if len(df) == 0:
        ax.text(0.5, 0.5, "No data to plot", ha='center', va='center')
        if title:
            ax.set_title(title)
        return

    # 设置时间轴
    if 'datetime' in df.columns:
        dates = df['datetime']
        if isinstance(dates.iloc[0], pd.Timestamp):
            ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(plt.matplotlib.dates.DayLocator(interval=max(1, len(df)//10)))
    else:
        dates = range(len(df))

    # 计算每个K线的宽度
    width = 0.6

    # 绘制K线
    for i, (idx, row) in enumerate(df.iterrows()):
        # 获取OHLC值
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']

        # 确定颜色
        color = 'red' if close_price > open_price else 'green'

        # 绘制影线
        ax.plot([i, i], [low_price, high_price], color=color, linewidth=1)

        # 绘制实体
        rect = plt.matplotlib.patches.Rectangle(
            xy=(i - width/2, min(open_price, close_price)),
            width=width,
            height=abs(close_price - open_price),
            facecolor=color,
            edgecolor=color,
            alpha=0.8
        )
        ax.add_patch(rect)

    # 设置标题和标签
    if title:
        ax.set_title(title)
    ax.set_ylabel('Price')
    ax.grid(True, alpha=0.3)

    # 设置x轴刻度
    ax.set_xticks(range(len(df)))
    if 'datetime' in df.columns:
        date_labels = [d.strftime('%Y-%m-%d') if isinstance(d, pd.Timestamp) else str(i)
                      for i, d in enumerate(df['datetime'])]
        ax.set_xticklabels(date_labels, rotation=45)

    # 自动调整y轴范围
    ax.autoscale_view()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试CandlestickVQGPT模型')

    # 数据参数
    parser.add_argument('--data_path', type=str, required=True, help='数据路径')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')

    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--codebook_path', type=str, default=None, help='码本权重路径')
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=4, help='嵌入维度')
    parser.add_argument('--atr_period', type=int, default=14, help='ATR周期')
    parser.add_argument('--ma_volume_period', type=int, default=20, help='成交量移动平均周期')
    parser.add_argument('--vectorization_method', type=str, default='percent_change',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    parser.add_argument('--code_size', type=int, default=100, help='证券代码数量')
    parser.add_argument('--use_time_features', action='store_true', help='是否使用时间特征')

    # 预测参数
    parser.add_argument('--seq_len', type=int, default=30, help='输入序列长度')
    parser.add_argument('--pred_len', type=int, default=5, help='预测长度')
    parser.add_argument('--temperature', type=float, default=1.0, help='采样温度')
    parser.add_argument('--top_k', type=int, default=50, help='Top-K采样')

    # 其他参数
    parser.add_argument('--output_file', type=str, default='prediction_result.png', help='输出文件路径')
    parser.add_argument('--cpu', action='store_true', help='强制使用CPU')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    args = parser.parse_args()

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    # 加载模型和tokenizer
    model, tokenizer, device = load_model_and_tokenizer(args)

    # 加载数据
    logger.info(f"加载数据: {args.data_path}")
    _, code_ids, data, _ = load_single_data(args.data_path)

    # 过滤数据
    if args.begin_date or args.end_date:
        filtered_data = []
        filtered_code_ids = []

        for i, df in enumerate(data):
            if 'datetime' in df.columns:
                if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
                    df['datetime'] = pd.to_datetime(df['datetime'])

                if args.begin_date:
                    begin_date = pd.to_datetime(args.begin_date)
                    df = df[df['datetime'] >= begin_date]

                if args.end_date:
                    end_date = pd.to_datetime(args.end_date)
                    df = df[df['datetime'] <= end_date]

            if len(df) >= args.seq_len:
                filtered_data.append(df)
                filtered_code_ids.append(code_ids[i])

        data = filtered_data
        code_ids = filtered_code_ids

    logger.info(f"加载了 {len(data)} 个证券的数据")

    # 选择第一个证券进行预测
    if len(data) > 0:
        df = data[0]
        code_id = code_ids[0]

        logger.info(f"使用证券代码 {code_id} 进行预测")

        # 预测未来的K线
        predicted_candles = predict_next_candles(model, tokenizer, df, code_id, device, args)

        # 绘制预测结果
        plot_predictions(df, predicted_candles, args)
    else:
        logger.error("没有找到符合条件的数据")

if __name__ == "__main__":
    main()
