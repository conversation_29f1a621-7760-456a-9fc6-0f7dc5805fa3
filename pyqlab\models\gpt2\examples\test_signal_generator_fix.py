"""
测试信号生成器修复

验证数组到标量转换的修复是否正确工作。
"""

import os
import sys
import numpy as np
import torch
import torch.nn.functional as F

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.gpt2.signal_generator import TopKStrategy


def test_topk_strategy_with_arrays():
    """测试TopKStrategy处理数组输入"""
    print("=== 测试TopKStrategy处理数组输入 ===")
    
    # 创建策略
    strategy = TopKStrategy(k=5, min_prob=0.1)
    
    # 创建模拟的logits
    vocab_size = 518
    logits = torch.randn(1, 30, vocab_size)
    
    # 模拟tokenizer
    class MockTokenizer:
        def __init__(self):
            self.num_embeddings = vocab_size
            
        def get_quantized_vector(self, token_id):
            # 模拟返回量化向量
            if 0 <= token_id < self.num_embeddings:
                return np.array([0.1, 0.2, 0.3, 0.4])  # 4维向量
            return None
            
        def vector_to_candlestick(self, vector, prev_close, ma_volume, atr_val):
            # 模拟向量到K线的转换
            change = vector[0] * atr_val
            return {
                'open': prev_close + change * 0.5,
                'high': prev_close + change * 1.2,
                'low': prev_close + change * 0.3,
                'close': prev_close + change,
                'volume': ma_volume * (1 + vector[1])
            }
    
    tokenizer = MockTokenizer()
    
    # 测试信号生成
    try:
        signal = strategy.generate_signal(
            logits=logits,
            tokenizer=tokenizer,
            temperature=1.5,
            prev_close=100.0,
            ma_volume=1000.0,
            atr=2.0
        )
        
        print("信号生成成功!")
        print(f"信号: {signal['signal']}")
        print(f"置信度: {signal['confidence']:.4f}")
        print(f"Top预测数量: {len(signal.get('top_predictions', []))}")
        
        # 检查预测详情
        if 'top_predictions' in signal:
            for i, pred in enumerate(signal['top_predictions'][:3]):
                print(f"预测 {i+1}: {pred['token']}, 概率: {pred['probability']:.4f}")
                if 'change' in pred:
                    print(f"  变化: {pred['change']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_array_conversion():
    """测试数组到标量的转换逻辑"""
    print("\n=== 测试数组到标量转换 ===")
    
    # 测试numpy数组
    np_array = np.array([315, 20, 120])
    torch_tensor = torch.tensor([0.19493379, 0.15234567, 0.12345678])
    
    print("原始数据:")
    print(f"  np_array: {np_array} (type: {type(np_array[0])})")
    print(f"  torch_tensor: {torch_tensor} (type: {type(torch_tensor[0])})")
    
    # 测试转换
    converted_results = []
    for token_idx, prob in zip(np_array, torch_tensor):
        # 应用转换逻辑
        token_idx_scalar = int(token_idx) if hasattr(token_idx, 'item') else int(token_idx)
        prob_scalar = float(prob) if hasattr(prob, 'item') else float(prob)
        
        converted_results.append((token_idx_scalar, prob_scalar))
        print(f"转换: {token_idx} -> {token_idx_scalar} (type: {type(token_idx_scalar)})")
        print(f"转换: {prob} -> {prob_scalar} (type: {type(prob_scalar)})")
    
    print("转换成功!")
    return True


def test_temperature_and_sampling():
    """测试温度和采样参数的效果"""
    print("\n=== 测试温度和采样效果 ===")
    
    # 创建偏向某些token的logits
    vocab_size = 518
    logits = torch.randn(1, 30, vocab_size)
    
    # 让某些token的logits更高
    logits[0, -1, 315] += 3.0
    logits[0, -1, 20] += 2.5
    logits[0, -1, 120] += 2.0
    
    # 测试不同温度
    temperatures = [0.8, 1.5, 2.0]
    
    for temp in temperatures:
        print(f"\n温度 {temp}:")
        
        # 应用温度
        scaled_logits = logits[0, -1] / temp
        probs = F.softmax(scaled_logits, dim=-1).numpy()
        
        # 获取top-5
        top_indices = np.argsort(probs)[::-1][:5]
        top_probs = probs[top_indices]
        
        print(f"  Top-5 indices: {top_indices}")
        print(f"  Top-5 probs: {top_probs}")
        print(f"  最高概率: {top_probs[0]:.4f}")
        print(f"  熵值: {-np.sum(probs * np.log(probs + 1e-10)):.4f}")
    
    return True


def main():
    """主函数"""
    print("测试信号生成器修复")
    print("=" * 50)
    
    # 运行测试
    tests = [
        test_array_conversion,
        test_temperature_and_sampling,
        test_topk_strategy_with_arrays
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 失败: {str(e)}")
            results.append(False)
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_func.__name__}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！信号生成器修复成功。")
        print("\n现在可以安全地运行回测脚本了：")
        print("python backtest_candlestick_vq_gpt.py --temperature 1.5 --top_k 30 --top_p 0.9 ...")
    else:
        print("❌ 部分测试失败，需要进一步修复。")


if __name__ == "__main__":
    main()
