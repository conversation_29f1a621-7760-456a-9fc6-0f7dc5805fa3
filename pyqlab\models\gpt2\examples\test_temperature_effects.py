"""
测试温度参数对模型预测多样性的影响

该脚本用于快速测试不同温度参数下模型预测的变化。
"""

import os
import sys
import numpy as np
import torch
import torch.nn.functional as F

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod


def test_temperature_effects():
    """测试温度参数效果"""
    print("=== 测试温度参数对预测多样性的影响 ===\n")
    
    # 创建一个简单的测试用例
    vocab_size = 518
    seq_len = 30
    
    # 模拟一个简单的logits输出（模拟过度集中的预测）
    # 创建一个偏向某些token的logits分布
    logits = torch.randn(1, seq_len, vocab_size)
    
    # 人为地让某些token的logits更高，模拟过度集中的问题
    logits[0, -1, 315] += 3.0  # 让token 315的logits更高
    logits[0, -1, 20] += 2.5   # 让token 20的logits较高
    logits[0, -1, 120] += 2.0  # 让token 120的logits较高
    logits[0, -1, 348] += 1.8  # 让token 348的logits较高
    logits[0, -1, 253] += 1.5  # 让token 253的logits较高
    
    last_logits = logits[0, -1, :]
    
    # 测试不同的温度参数
    temperatures = [0.1, 0.5, 0.8, 1.0, 1.5, 2.0, 3.0]
    
    print("原始logits统计:")
    print(f"  最小值: {last_logits.min().item():.4f}")
    print(f"  最大值: {last_logits.max().item():.4f}")
    print(f"  平均值: {last_logits.mean().item():.4f}")
    print(f"  标准差: {last_logits.std().item():.4f}")
    print()
    
    for temp in temperatures:
        # 应用温度缩放
        scaled_logits = last_logits / temp
        probs = F.softmax(scaled_logits, dim=-1).numpy()
        
        # 获取top-5预测
        top_indices = np.argsort(probs)[::-1][:5]
        top_probs = probs[top_indices]
        
        # 计算熵值（衡量预测的不确定性）
        entropy = -np.sum(probs * np.log(probs + 1e-10))
        max_entropy = np.log(vocab_size)
        entropy_ratio = entropy / max_entropy
        
        # 计算有效预测数量（概率>1%的token数量）
        effective_tokens = np.sum(probs > 0.01)
        
        print(f"温度 {temp}:")
        print(f"  Top-5 indices: {top_indices}")
        print(f"  Top-5 probs: {top_probs}")
        print(f"  熵值: {entropy:.4f} / {max_entropy:.4f} ({entropy_ratio:.2%})")
        print(f"  有效token数量: {effective_tokens} ({effective_tokens/vocab_size:.2%})")
        print(f"  最高概率: {top_probs[0]:.4f}")
        print()


def test_nucleus_sampling():
    """测试nucleus采样的效果"""
    print("=== 测试Nucleus采样的效果 ===\n")
    
    vocab_size = 518
    
    # 创建一个偏向某些token的logits分布
    logits = torch.randn(vocab_size)
    logits[315] += 3.0
    logits[20] += 2.5
    logits[120] += 2.0
    logits[348] += 1.8
    logits[253] += 1.5
    
    # 计算原始概率
    probs = F.softmax(logits, dim=-1).numpy()
    
    # 测试不同的top_p值
    top_p_values = [0.5, 0.7, 0.8, 0.9, 0.95, 0.99]
    
    print("原始概率分布:")
    top_indices = np.argsort(probs)[::-1][:10]
    top_probs = probs[top_indices]
    print(f"  Top-10 indices: {top_indices}")
    print(f"  Top-10 probs: {top_probs}")
    print(f"  累积概率: {np.cumsum(top_probs)}")
    print()
    
    for top_p in top_p_values:
        # 实现nucleus采样
        sorted_indices = np.argsort(probs)[::-1]
        sorted_probs = probs[sorted_indices]
        cumsum_probs = np.cumsum(sorted_probs)
        
        # 找到累积概率超过top_p的位置
        cutoff_idx = np.where(cumsum_probs >= top_p)[0]
        if len(cutoff_idx) > 0:
            cutoff_idx = cutoff_idx[0] + 1  # 包含刚好超过top_p的token
        else:
            cutoff_idx = len(sorted_probs)
        
        # 保留top_p的token，其他设为0
        nucleus_probs = np.zeros_like(probs)
        nucleus_probs[sorted_indices[:cutoff_idx]] = sorted_probs[:cutoff_idx]
        
        # 重新归一化
        nucleus_probs = nucleus_probs / nucleus_probs.sum()
        
        # 计算统计信息
        effective_tokens = np.sum(nucleus_probs > 0)
        entropy = -np.sum(nucleus_probs * np.log(nucleus_probs + 1e-10))
        max_entropy = np.log(vocab_size)
        
        print(f"Top-p {top_p}:")
        print(f"  保留的token数量: {effective_tokens}")
        print(f"  实际累积概率: {cumsum_probs[cutoff_idx-1]:.4f}")
        print(f"  熵值: {entropy:.4f} / {max_entropy:.4f} ({entropy/max_entropy:.2%})")
        print(f"  最高概率: {nucleus_probs.max():.4f}")
        print()


def test_top_k_sampling():
    """测试top-k采样的效果"""
    print("=== 测试Top-K采样的效果 ===\n")
    
    vocab_size = 518
    
    # 创建一个偏向某些token的logits分布
    logits = torch.randn(vocab_size)
    logits[315] += 3.0
    logits[20] += 2.5
    logits[120] += 2.0
    logits[348] += 1.8
    logits[253] += 1.5
    
    # 计算原始概率
    probs = F.softmax(logits, dim=-1).numpy()
    
    # 测试不同的top_k值
    top_k_values = [5, 10, 20, 50, 100, 200]
    
    for top_k in top_k_values:
        # 实现top-k采样
        top_indices = np.argsort(probs)[::-1][:top_k]
        top_k_probs = np.zeros_like(probs)
        top_k_probs[top_indices] = probs[top_indices]
        
        # 重新归一化
        top_k_probs = top_k_probs / top_k_probs.sum()
        
        # 计算统计信息
        entropy = -np.sum(top_k_probs * np.log(top_k_probs + 1e-10))
        max_entropy = np.log(top_k)
        
        # 获取实际的top-5
        actual_top_indices = np.argsort(top_k_probs)[::-1][:5]
        actual_top_probs = top_k_probs[actual_top_indices]
        
        print(f"Top-k {top_k}:")
        print(f"  Top-5 indices: {actual_top_indices}")
        print(f"  Top-5 probs: {actual_top_probs}")
        print(f"  熵值: {entropy:.4f} / {max_entropy:.4f} ({entropy/max_entropy:.2%})")
        print(f"  最高概率: {top_k_probs.max():.4f}")
        print()


def main():
    """主函数"""
    print("测试不同采样策略对预测多样性的影响\n")
    print("这个测试使用模拟数据来演示各种采样策略的效果。")
    print("在实际应用中，您可以将这些参数应用到回测脚本中。\n")
    
    test_temperature_effects()
    test_nucleus_sampling()
    test_top_k_sampling()
    
    print("=== 建议 ===")
    print("基于以上测试结果，建议:")
    print("1. 使用较高的温度参数 (1.5-2.0) 来增加预测多样性")
    print("2. 结合使用top-k (20-50) 和nucleus采样 (top_p=0.8-0.9)")
    print("3. 在回测脚本中使用以下参数:")
    print("   --temperature 1.5 --top_k 30 --top_p 0.9")
    print("\n这些参数可以显著改善模型预测的多样性，")
    print("从而产生更多样化的交易信号。")


if __name__ == "__main__":
    main()
