"""
快速模型测试

直接测试模型是否存在预测多样性问题。
"""

import os
import sys
import numpy as np
import torch
import torch.nn.functional as F

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from backtest_candlestick_vq_gpt import create_tokenizer, load_model


def test_model_with_random_inputs(model, tokenizer, device, num_tests=20):
    """使用随机输入测试模型"""
    print("=== 使用随机输入测试模型 ===")
    
    model.eval()
    vocab_size = tokenizer.vocab_size
    
    all_predictions = []
    
    with torch.no_grad():
        for i in range(num_tests):
            # 生成随机输入
            seq_len = 30
            input_tokens = torch.randint(0, vocab_size, (1, seq_len), dtype=torch.int32).to(device)
            code_ids = torch.randint(0, 100, (1,), dtype=torch.int32).to(device)
            
            # 前向传播
            logits, _ = model(input_tokens, code_ids, time_features=None)
            last_logits = logits[0, -1]
            
            # 计算概率分布
            probs = F.softmax(last_logits, dim=-1).cpu().numpy()
            top_5_indices = np.argsort(probs)[::-1][:5]
            top_5_probs = probs[top_5_indices]
            
            entropy = -np.sum(probs * np.log(probs + 1e-10))
            max_entropy = np.log(vocab_size)
            
            all_predictions.append({
                'test': i,
                'top_5_indices': top_5_indices.tolist(),
                'top_5_probs': top_5_probs.tolist(),
                'entropy': entropy,
                'entropy_ratio': entropy / max_entropy
            })
            
            print(f"测试{i}: Top-5={top_5_indices}, 最高概率={top_5_probs[0]:.4f}, 熵比例={entropy/max_entropy:.2%}")
    
    # 分析结果
    print(f"\n=== 随机输入测试结果 ===")
    
    # 检查预测多样性
    all_top1 = [pred['top_5_indices'][0] for pred in all_predictions]
    unique_top1 = len(set(all_top1))
    print(f"唯一Top-1预测: {unique_top1}/{num_tests} ({unique_top1/num_tests:.1%})")
    
    # 检查概率分布
    max_probs = [pred['top_5_probs'][0] for pred in all_predictions]
    entropies = [pred['entropy'] for pred in all_predictions]
    entropy_ratios = [pred['entropy_ratio'] for pred in all_predictions]
    
    print(f"最高概率统计: 平均={np.mean(max_probs):.4f}, 标准差={np.std(max_probs):.4f}")
    print(f"熵值统计: 平均={np.mean(entropies):.4f}, 标准差={np.std(entropies):.4f}")
    print(f"熵比例统计: 平均={np.mean(entropy_ratios):.2%}, 标准差={np.std(entropy_ratios):.2%}")
    
    # 检查是否总是预测相同的token
    from collections import Counter
    top1_counter = Counter(all_top1)
    most_common = top1_counter.most_common(5)
    
    print(f"\n最常见的Top-1预测:")
    for token_id, count in most_common:
        print(f"  Token {token_id}: {count}次 ({count/num_tests:.1%})")
    
    # 判断是否存在问题
    issues = []
    if unique_top1 < num_tests * 0.3:
        issues.append(f"预测多样性过低: 只有{unique_top1}个不同的预测")
    
    if np.mean(max_probs) > 0.5:
        issues.append(f"预测过于确定: 平均最高概率{np.mean(max_probs):.1%}")
    
    if np.mean(entropy_ratios) < 0.5:
        issues.append(f"熵值过低: 平均熵比例{np.mean(entropy_ratios):.1%}")
    
    if most_common[0][1] > num_tests * 0.5:
        issues.append(f"过度集中预测: Token {most_common[0][0]} 出现{most_common[0][1]}次")
    
    return {
        'prediction_diversity': unique_top1 / num_tests,
        'avg_max_prob': np.mean(max_probs),
        'avg_entropy_ratio': np.mean(entropy_ratios),
        'most_common_token': most_common[0],
        'issues': issues
    }


def test_model_with_different_temperatures(model, tokenizer, device):
    """测试不同温度参数的效果"""
    print("\n=== 测试不同温度参数效果 ===")
    
    model.eval()
    vocab_size = tokenizer.vocab_size
    
    # 固定输入
    seq_len = 30
    input_tokens = torch.randint(0, vocab_size, (1, seq_len), dtype=torch.int32).to(device)
    code_ids = torch.randint(0, 100, (1,), dtype=torch.int32).to(device)
    
    temperatures = [0.1, 0.5, 1.0, 1.5, 2.0, 3.0, 5.0]
    
    with torch.no_grad():
        # 获取原始logits
        logits, _ = model(input_tokens, code_ids, time_features=None)
        original_logits = logits[0, -1]
        
        print("温度参数对预测的影响:")
        for temp in temperatures:
            # 应用温度
            scaled_logits = original_logits / temp
            probs = F.softmax(scaled_logits, dim=-1).cpu().numpy()
            
            # 获取top-5
            top_5_indices = np.argsort(probs)[::-1][:5]
            top_5_probs = probs[top_5_indices]
            
            # 计算熵
            entropy = -np.sum(probs * np.log(probs + 1e-10))
            max_entropy = np.log(vocab_size)
            entropy_ratio = entropy / max_entropy
            
            print(f"  温度{temp}: Top-5={top_5_indices}, 最高概率={top_5_probs[0]:.4f}, 熵比例={entropy_ratio:.2%}")


def test_model_architecture(model):
    """测试模型架构"""
    print("\n=== 模型架构测试 ===")
    
    print(f"模型参数:")
    print(f"  词汇表大小: {model.vocab_size}")
    print(f"  序列长度: {model.seq_len}")
    print(f"  模型维度: {model.d_model}")
    print(f"  注意力头数: {model.n_head}")
    print(f"  层数: {model.n_layer}")
    print(f"  总参数数: {model.get_num_params():,}")
    
    # 检查模型是否过小
    issues = []
    if model.get_num_params() < 100000:
        issues.append("模型参数数量可能过少")
    
    if model.d_model < 64:
        issues.append("模型维度可能过小")
    
    if model.n_layer < 4:
        issues.append("模型层数可能过少")
    
    return issues


def main():
    """主函数"""
    print("🔍 快速模型测试")
    print("=" * 50)
    
    # 模型路径
    model_path = "E:/lab/RoboQuant/pylab/models/candlestick_vq_gpt/best_model.pt"
    codebook_path = "E:/lab/RoboQuant/pylab/models/vqvae/vqvae_20250522/vqcb_atr_based_fut_top_min1_512_0.0201.pt"
    
    # 创建参数对象
    class Args:
        def __init__(self):
            self.model_path = model_path
            self.codebook_path = codebook_path
            self.vectorization_method = 'atr_based'
            self.use_code_dim = True
            self.code_dim = 5
            self.seq_len = 30
            self.use_time_features = False
    
    args = Args()
    
    try:
        # 创建tokenizer和加载模型
        print("创建Tokenizer...")
        tokenizer = create_tokenizer(args)
        print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")
        
        print("加载模型...")
        model, device = load_model(args, tokenizer)
        print(f"模型加载成功，设备: {device}")
        
        # 运行测试
        print("\n" + "=" * 50)
        
        # 1. 模型架构测试
        arch_issues = test_model_architecture(model)
        
        # 2. 随机输入测试
        random_results = test_model_with_random_inputs(model, tokenizer, device, 20)
        
        # 3. 温度参数测试
        test_model_with_different_temperatures(model, tokenizer, device)
        
        # 生成诊断报告
        print("\n" + "=" * 50)
        print("🔍 快速诊断报告")
        print("=" * 50)
        
        all_issues = arch_issues + random_results['issues']
        
        if all_issues:
            print("❌ 发现的问题:")
            for i, issue in enumerate(all_issues, 1):
                print(f"  {i}. {issue}")
            
            print("\n💡 建议的解决方案:")
            
            if random_results['prediction_diversity'] < 0.3:
                print("  🔥 关键问题: 模型预测多样性极低")
                print("     - 这表明模型可能存在严重的过拟合或训练问题")
                print("     - 立即尝试: 使用temperature=3.0-5.0进行推理")
                print("     - 长期解决: 重新训练模型，增加正则化")
            
            if random_results['avg_max_prob'] > 0.8:
                print("  🔥 关键问题: 模型预测过于确定")
                print("     - 模型输出的概率分布过于尖锐")
                print("     - 立即尝试: 使用更高的温度参数")
            
            if "模型参数数量可能过少" in arch_issues:
                print("  ⚠️  模型容量可能不足")
                print("     - 考虑增加模型层数或维度")
                print("     - 重新训练更大的模型")
            
            print("\n🔧 立即可用的解决方案:")
            print("  1. 在回测中使用: --temperature 3.0 --top_k 20")
            print("  2. 如果仍然无效，使用: --temperature 5.0 --top_k 10")
            print("  3. 考虑重新训练模型")
            
        else:
            print("✅ 模型看起来正常")
            print("   问题可能出现在数据处理或其他地方")
        
        print(f"\n📊 关键指标:")
        print(f"  预测多样性: {random_results['prediction_diversity']:.1%}")
        print(f"  平均最高概率: {random_results['avg_max_prob']:.1%}")
        print(f"  平均熵比例: {random_results['avg_entropy_ratio']:.1%}")
        print(f"  最常见token: {random_results['most_common_token'][0]} ({random_results['most_common_token'][1]}次)")
        
        # 给出明确的建议
        if random_results['prediction_diversity'] < 0.1:
            print(f"\n🚨 严重问题: 预测多样性只有{random_results['prediction_diversity']:.1%}")
            print("   这确实表明模型存在严重问题，需要重新训练或使用极高的温度参数")
        elif random_results['prediction_diversity'] < 0.3:
            print(f"\n⚠️  中等问题: 预测多样性{random_results['prediction_diversity']:.1%}偏低")
            print("   可以通过调整推理参数来改善")
        else:
            print(f"\n✅ 预测多样性{random_results['prediction_diversity']:.1%}看起来正常")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
