import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Tuple


class SignalStrategy(ABC):
    """
    交易信号策略的抽象基类
    """
    @abstractmethod
    def generate_signal(self, logits: torch.Tensor, tokenizer: Any, **kwargs) -> Dict[str, Any]:
        """
        根据模型输出生成交易信号

        Args:
            logits: 模型输出的logits
            tokenizer: 用于解码的tokenizer
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        pass


class ThresholdDirectionStrategy(SignalStrategy):
    """
    基于方向概率阈值的信号生成策略
    """
    def __init__(self, threshold: float = 0.6):
        self.threshold = threshold

    def generate_signal(self, logits: torch.Tensor, tokenizer: Any, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        # 获取最后一个时间步的logits
        last_logits = logits[0, -1] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        # 计算方向概率
        direction_probs = {'up': 0.0, 'flat': 0.0, 'down': 0.0}

        # 检查tokenizer类型
        is_vq_tokenizer = hasattr(tokenizer, 'get_quantized_vector') and hasattr(tokenizer, 'vector_to_candlestick')

        if is_vq_tokenizer:
            # 对于VQ Tokenizer，遍历所有可能的token ID
            for i in range(len(probs)):
                try:
                    # 获取量化向量
                    quantized_vector = tokenizer.get_quantized_vector(i)

                    if quantized_vector is not None:
                        # 使用一个基准值来解析向量
                        prev_close = 100.0  # 假设的基准收盘价
                        ma_volume = 1000.0  # 假设的基准成交量
                        atr_val = 1.0       # 假设的ATR值

                        # 将向量转换为K线数据
                        candlestick = tokenizer.vector_to_candlestick(quantized_vector, prev_close, ma_volume, atr_val)

                        # 计算change值（收盘价相对于前一收盘价的变化）
                        change_value = candlestick['close'] - prev_close

                        prob = probs[i]

                        if change_value > 0:
                            direction_probs['up'] += prob
                        elif change_value < 0:
                            direction_probs['down'] += prob
                        else:
                            direction_probs['flat'] += prob
                except Exception as e:
                    # 忽略错误，继续处理下一个token
                    continue
        else:
            # 对于传统Tokenizer，使用字符串解析
            direction_probs = {
                'up': sum(probs[i] for i, token in enumerate(tokenizer.idx2token.values())
                         if '|' in token and int(token.split('|')[0]) > 0),
                'flat': sum(probs[i] for i, token in enumerate(tokenizer.idx2token.values())
                           if '|' in token and int(token.split('|')[0]) == 0),
                'down': sum(probs[i] for i, token in enumerate(tokenizer.idx2token.values())
                           if '|' in token and int(token.split('|')[0]) < 0)
            }

        # 生成信号
        if direction_probs['up'] > self.threshold:
            signal = {'signal': 'BUY', 'confidence': direction_probs['up']}
        elif direction_probs['down'] > self.threshold:
            signal = {'signal': 'SELL', 'confidence': direction_probs['down']}
        else:
            signal = {'signal': 'HOLD', 'confidence': max(direction_probs.values())}

        # 添加方向概率信息
        signal['direction_probs'] = direction_probs

        return signal


class TopKStrategy(SignalStrategy):
    """
    基于TopK预测的信号生成策略
    """
    def __init__(self, k: int = 3, min_prob: float = 0.003):
        self.k = k
        self.min_prob = min_prob

    def generate_signal(self, logits: torch.Tensor, tokenizer: Any, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        # 获取最后一个时间步的logits
        if logits.dim() == 3:
            # 如果是3D张量 (batch_size, seq_len, vocab_size)，取最后一个时间步
            last_logits = logits[0, -1] / temperature
        elif logits.dim() == 2:
            # 如果是2D张量 (seq_len, vocab_size)，取最后一个时间步
            last_logits = logits[-1] / temperature
        else:
            # 如果是1D张量 (vocab_size)，直接使用
            last_logits = logits / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()
        # print(f"probs: {probs}")

        # 获取top-k预测
        top_indices = np.argsort(probs)[::-1][:self.k]
        top_probs = probs[top_indices]
        print(f"top_indices: {top_indices}")
        print(f"top_probs: {top_probs}")

        # 解析预测
        parsed_predictions = []

        # 检查tokenizer类型
        is_vq_tokenizer = hasattr(tokenizer, 'get_quantized_vector') and hasattr(tokenizer, 'vector_to_candlestick')

        for token_idx, prob in zip(top_indices, top_probs):
            # 确保token_idx和prob是标量值
            token_idx = int(token_idx) if hasattr(token_idx, 'item') else int(token_idx)
            prob = float(prob) if hasattr(prob, 'item') else float(prob)

            if is_vq_tokenizer:
                # 对于VQ Tokenizer，使用量化向量解析
                try:
                    # 获取量化向量
                    quantized_vector = tokenizer.get_quantized_vector(token_idx)

                    if quantized_vector is not None:
                        # 使用一个基准值来解析向量（这里使用简化的方法）
                        # 在实际应用中，可能需要从上下文中获取更准确的基准值
                        if kwargs:
                            prev_close = kwargs['prev_close']
                            ma_volume = kwargs['ma_volume']
                            atr_val = kwargs['atr']
                            # print(f"prev_close: {prev_close}, ma_volume: {ma_volume}, atr_val: {atr_val}")
                        else:
                            raise ValueError("Missing context information for VQ token parsing")

                        # 将向量转换为K线数据
                        # print(f"quantized_vector: {quantized_vector}")
                        candlestick = tokenizer.vector_to_candlestick(quantized_vector, prev_close, ma_volume, atr_val)
                        # print(f"candlestick: {candlestick}")

                        # 计算change值（收盘价相对于前一收盘价的变化）
                        change_value = candlestick['close'] - prev_close

                        # 标准化change值
                        if atr_val > 0:
                            normalized_change = round(change_value / atr_val, 3)
                        else:
                            normalized_change = 0
                        # print(f"change_value: {change_value} / {atr_val}  {normalized_change}")

                        parsed_predictions.append({
                            'token': f"VQ_TOKEN_{token_idx}",
                            'change': normalized_change,
                            'close': candlestick['close'],
                            'open': candlestick['open'],
                            'high': candlestick['high'],
                            'low': candlestick['low'],
                            'probability': prob
                        })
                    else:
                        # 处理特殊token或无效token
                        parsed_predictions.append({
                            'token': f"SPECIAL_TOKEN_{token_idx}",
                            'special': True,
                            'probability': prob
                        })
                except Exception as e:
                    print(f"解析VQ token {token_idx}时出错: {str(e)}")
                    parsed_predictions.append({
                        'token': f"ERROR_TOKEN_{token_idx}",
                        'special': True,
                        'probability': prob
                    })
            else:
                # 对于传统Tokenizer，使用字符串解析
                token = tokenizer.idx2token[token_idx]
                if '|' in token:
                    parts = token.split('|')
                    if len(parts) == 4:
                        change, entity, upline, downline = parts
                        parsed_predictions.append({
                            'token': token,
                            'change': int(change),
                            'entity': int(entity),
                            'upline': int(upline),
                            'downline': int(downline),
                            'probability': prob
                        })
                else:
                    parsed_predictions.append({
                        'token': token,
                        'special': True,
                        'probability': prob
                    })

        # 基于top-1预测生成信号
        if parsed_predictions and ('change' in parsed_predictions[0] or 'close' in parsed_predictions[0]):
            top_pred = parsed_predictions[0]
            prob = top_pred['probability']
            print(f"Top prob: {prob:.6f} {top_pred['token']}")

            if prob >= self.min_prob:
                # 获取change值
                if 'change' in top_pred:
                    change_value = top_pred['change']
                    print(f"change_value: {change_value}")
                elif 'close' in top_pred and 'prev_close' in top_pred:
                    # 如果有close和prev_close，直接计算变化
                    change_value = top_pred['close'] - top_pred['prev_close']
                else:
                    change_value = 0

                if change_value > 0:
                    signal = {'signal': 'BUY', 'confidence': prob}
                elif change_value < 0:
                    signal = {'signal': 'SELL', 'confidence': prob}
                else:
                    signal = {'signal': 'HOLD', 'confidence': prob}
            else:
                signal = {'signal': 'HOLD', 'confidence': prob}
        else:
            signal = {'signal': 'HOLD', 'confidence': 0.0}

        # 添加详细预测信息
        signal['top_predictions'] = parsed_predictions

        return signal


class WeightedEnsembleStrategy(SignalStrategy):
    """
    多策略加权集成的信号生成策略
    """
    def __init__(self, strategies: List[Tuple[SignalStrategy, float]]):
        """
        初始化加权集成策略

        Args:
            strategies: 策略和权重的列表，如 [(strategy1, 0.7), (strategy2, 0.3)]
        """
        self.strategies = strategies
        # 归一化权重
        total_weight = sum(weight for _, weight in strategies)
        self.normalized_weights = [weight/total_weight for _, weight in strategies]

    def generate_signal(self, logits: torch.Tensor, tokenizer: Any, **kwargs) -> Dict[str, Any]:
        # 收集各策略的信号
        signals = []
        for (strategy, _), weight in zip(self.strategies, self.normalized_weights):
            signal = strategy.generate_signal(logits, tokenizer, **kwargs)
            signals.append((signal, weight))

        # 计算加权信号
        buy_confidence = sum(signal['confidence'] * weight for signal, weight in signals
                            if signal['signal'] == 'BUY')
        sell_confidence = sum(signal['confidence'] * weight for signal, weight in signals
                             if signal['signal'] == 'SELL')
        hold_confidence = sum(signal['confidence'] * weight for signal, weight in signals
                             if signal['signal'] == 'HOLD')

        # 确定最终信号
        if buy_confidence > sell_confidence and buy_confidence > hold_confidence:
            final_signal = {'signal': 'BUY', 'confidence': buy_confidence}
        elif sell_confidence > buy_confidence and sell_confidence > hold_confidence:
            final_signal = {'signal': 'SELL', 'confidence': sell_confidence}
        else:
            final_signal = {'signal': 'HOLD', 'confidence': hold_confidence}

        # 添加详细信息
        final_signal['component_signals'] = [signal for signal, _ in signals]
        final_signal['weights'] = self.normalized_weights

        return final_signal


class StatisticalMomentumStrategy(SignalStrategy):
    """
    基于统计动量的信号生成策略
    """
    def __init__(self, momentum_threshold: float = 0.2, min_prob: float = 0.01):
        self.momentum_threshold = momentum_threshold
        self.min_prob = min_prob

    def generate_signal(self, logits: torch.Tensor, tokenizer: Any, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        # 获取最后一个时间步的logits
        last_logits = logits[0, -1] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        # 计算上涨和下跌的统计动量
        up_momentum = 0
        down_momentum = 0

        # 检查tokenizer类型
        is_vq_tokenizer = hasattr(tokenizer, 'get_quantized_vector') and hasattr(tokenizer, 'vector_to_candlestick')

        if is_vq_tokenizer:
            # 对于VQ Tokenizer，遍历所有可能的token ID
            for i in range(len(probs)):
                try:
                    # 获取量化向量
                    quantized_vector = tokenizer.get_quantized_vector(i)

                    if quantized_vector is not None:
                        # 使用一个基准值来解析向量
                        prev_close = 100.0  # 假设的基准收盘价
                        ma_volume = 1000.0  # 假设的基准成交量
                        atr_val = 1.0       # 假设的ATR值

                        # 将向量转换为K线数据
                        candlestick = tokenizer.vector_to_candlestick(quantized_vector, prev_close, ma_volume, atr_val)

                        # 计算change值（收盘价相对于前一收盘价的变化）
                        change_value = candlestick['close'] - prev_close

                        # 标准化change值
                        if atr_val > 0:
                            normalized_change = change_value / atr_val
                        else:
                            normalized_change = 0

                        prob = probs[i]

                        if normalized_change > 0:
                            # 上涨动量 = 变化幅度 * 概率
                            up_momentum += normalized_change * prob
                        elif normalized_change < 0:
                            # 下跌动量 = 变化幅度 * 概率 (取绝对值)
                            down_momentum += abs(normalized_change) * prob
                except Exception as e:
                    # 忽略错误，继续处理下一个token
                    continue
        else:
            # 对于传统Tokenizer，使用字符串解析
            for i, token in enumerate(tokenizer.idx2token.values()):
                if '|' in token:
                    parts = token.split('|')
                    if len(parts) == 4:
                        change = int(parts[0])
                        prob = probs[i]

                        if change > 0:
                            # 上涨动量 = 变化幅度 * 概率
                            up_momentum += change * prob
                        elif change < 0:
                            # 下跌动量 = 变化幅度 * 概率 (取绝对值)
                            down_momentum += abs(change) * prob

        # 生成信号
        if up_momentum > self.momentum_threshold and up_momentum > down_momentum:
            signal = {'signal': 'BUY', 'confidence': min(1.0, up_momentum)}
        elif down_momentum > self.momentum_threshold and down_momentum > up_momentum:
            signal = {'signal': 'SELL', 'confidence': min(1.0, down_momentum)}
        else:
            signal = {'signal': 'HOLD', 'confidence': max(up_momentum, down_momentum)}

        # 添加动量信息
        signal['up_momentum'] = float(up_momentum)
        signal['down_momentum'] = float(down_momentum)

        return signal


class SignalGenerator:
    """
    交易信号生成器
    """
    def __init__(self, strategy: SignalStrategy):
        self.strategy = strategy

    def generate_signal(self, logits: torch.Tensor, tokenizer: Any, **kwargs) -> Dict[str, Any]:
        """
        生成交易信号

        Args:
            logits: 模型输出的logits
            tokenizer: 用于解码的tokenizer
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        return self.strategy.generate_signal(logits, tokenizer, **kwargs)

    def set_strategy(self, strategy: SignalStrategy):
        """
        设置信号生成策略

        Args:
            strategy: 新的信号生成策略
        """
        self.strategy = strategy


class RealTimePredictionService:
    """实时预测服务"""
    def __init__(self, model, tokenizer, signal_generator=None, device='cpu'):
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        self.model.to(device)
        self.model.eval()

        # 如果没有提供信号生成器，使用默认的阈值方向策略
        if signal_generator is None:
            default_strategy = ThresholdDirectionStrategy(threshold=0.6)
            self.signal_generator = SignalGenerator(default_strategy)
        else:
            self.signal_generator = signal_generator

        # 缓存最近的数据和预测
        self.recent_data = {}
        self.recent_predictions = {}

    def update_data(self, symbol, new_candle):
        """更新数据"""
        if symbol not in self.recent_data:
            self.recent_data[symbol] = []

        # 添加新K线
        self.recent_data[symbol].append(new_candle)

        # 保持最近的N条K线
        max_candles = 100  # 保留足够多的K线以便进行预测
        if len(self.recent_data[symbol]) > max_candles:
            self.recent_data[symbol] = self.recent_data[symbol][-max_candles:]

    def predict(self, symbol, code_id, seq_len=30, temperature=0.8, time_features=None):
        """生成预测"""
        if symbol not in self.recent_data or len(self.recent_data[symbol]) < seq_len:
            return None

        # 准备数据
        recent_candles = self.recent_data[symbol][-seq_len:]
        df = pd.DataFrame(recent_candles)

        # Tokenize
        tokens = self.tokenizer.tokenize(df)

        # 准备输入
        input_tokens = torch.tensor(tokens, dtype=torch.int32).unsqueeze(0).to(self.device)
        code_tensor = torch.tensor([code_id], dtype=torch.int32).to(self.device)

        # 生成预测
        with torch.no_grad():
            if time_features is not None:
                tf = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(self.device)
                logits, _ = self.model(input_tokens, code_tensor, time_features=tf)
            else:
                logits, _ = self.model(input_tokens, code_tensor)

        # 使用信号生成器生成信号
        signal = self.signal_generator.generate_signal(
            logits=logits,
            tokenizer=self.tokenizer,
            temperature=temperature
        )

        # 保存预测
        self.recent_predictions[symbol] = {
            'timestamp': pd.Timestamp.now(),
            'signal': signal
        }

        return self.recent_predictions[symbol]

    def get_trading_signal(self, symbol):
        """获取交易信号"""
        if symbol not in self.recent_predictions:
            return {'signal': 'HOLD', 'confidence': 0.0}

        return self.recent_predictions[symbol]['signal']

    def set_signal_strategy(self, strategy):
        """设置信号生成策略"""
        self.signal_generator.set_strategy(strategy)


# 使用示例
def create_example_service():
    """创建示例服务（仅用于演示）"""
    # 这里应该导入实际的模型和tokenizer
    # 以下仅为示例代码
    class DummyModel:
        def __init__(self):
            pass

        def to(self, device):
            return self

        def eval(self):
            pass

        def __call__(self, input_tokens, code_tensor, time_features=None):
            # 返回随机logits
            batch_size = input_tokens.shape[0]
            seq_len = input_tokens.shape[1]
            vocab_size = 100
            logits = torch.randn(batch_size, seq_len, vocab_size)
            return logits, None

    class DummyTokenizer:
        def __init__(self):
            self.idx2token = {i: f"{i-50}|{i%10}|{i%5}|{i%3}" for i in range(100)}

        def tokenize(self, df):
            return [1, 2, 3, 4, 5]  # 示例tokens

    # 创建服务
    model = DummyModel()
    tokenizer = DummyTokenizer()

    # 创建不同的策略
    threshold_strategy = ThresholdDirectionStrategy(threshold=0.6)
    topk_strategy = TopKStrategy(k=3, min_prob=0.3)
    momentum_strategy = StatisticalMomentumStrategy(momentum_threshold=0.2)

    # 创建集成策略
    ensemble_strategy = WeightedEnsembleStrategy([
        (threshold_strategy, 0.5),
        (topk_strategy, 0.3),
        (momentum_strategy, 0.2)
    ])

    # 创建信号生成器
    signal_generator = SignalGenerator(ensemble_strategy)

    # 创建服务
    service = RealTimePredictionService(model, tokenizer, signal_generator)

    return service
