"""
修复CandlestickVQGPT模型预测多样性问题

该脚本提供几种方法来改善模型预测的多样性。
"""

import os
import sys
import argparse
import json
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
from pyqlab.models.gpt2.vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from pyqlab.models.gpt2.utils import load_single_data, extract_time_features


def create_tokenizer(args):
    """创建Tokenizer"""
    # 选择向量化方法
    if args.vectorization_method == 'atr_based':
        vectorization_method = VectorizationMethod.ATR_BASED
    elif args.vectorization_method == 'percent_change':
        vectorization_method = VectorizationMethod.PERCENT_CHANGE
    elif args.vectorization_method == 'log_return':
        vectorization_method = VectorizationMethod.LOG_RETURN
    elif args.vectorization_method == 'zscore':
        vectorization_method = VectorizationMethod.ZSCORE
    elif args.vectorization_method == 'minmax':
        vectorization_method = VectorizationMethod.MINMAX
    else:
        raise ValueError(f"未知的向量化方法: {args.vectorization_method}")

    # 从配置文件中获取参数
    config = {}
    if args.model_path:
        config_path = os.path.join(os.path.dirname(args.model_path), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)

    # 创建Tokenizer
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=config.get('num_embeddings', 512),
        embedding_dim=config.get('embedding_dim', 4),
        atr_period=config.get('atr_period', 14),
        ma_volume_period=config.get('ma_volume_period', 14),
        vectorization_method=vectorization_method,
        detect_gaps=True,
        gap_threshold=7.0,
        use_code_dim=False,
        code_dim=16
    )

    return tokenizer


def load_model(args, tokenizer):
    """加载模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载检查点
    checkpoint = torch.load(args.model_path, map_location=device)
    config = checkpoint.get('config', {})
    
    # 使用tokenizer的词汇表大小
    vocab_size = tokenizer.vocab_size
    
    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=vocab_size,
        code_size=config.get('code_size', 100),
        seq_len=config.get('seq_len', config.get('block_size', 30)),
        n_layer=config.get('n_layer', 4),
        n_head=config.get('n_head', 8),
        d_model=config.get('d_model', 128),
        dropout=0.0,
        bias=False,
        use_time_features=config.get('use_time_features', True),
        n_time_features=config.get('n_time_features', 8),
        label_smoothing=0.0,
        use_auxiliary_loss=False
    )
    
    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)
    
    return model, device


def test_temperature_effects(model, tokenizer, sample_data, device, temperatures=[0.1, 0.5, 0.8, 1.0, 1.5, 2.0]):
    """测试不同温度参数的效果"""
    print("\n=== 测试温度参数效果 ===")
    
    # 准备一个样本
    df, code_id = sample_data[0]
    tokens = tokenizer.tokenize(df)
    
    input_tokens = torch.tensor(tokens[-30:], dtype=torch.int32).unsqueeze(0).to(device)
    code_ids = torch.tensor([code_id], dtype=torch.int32).to(device)
    time_features = extract_time_features(df.iloc[-30:])
    time_features = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(device)
    
    model.eval()
    with torch.no_grad():
        logits, _ = model(input_tokens, code_ids, time_features)
        last_logits = logits[0, -1, :]
        
        for temp in temperatures:
            # 应用温度
            scaled_logits = last_logits / temp
            probs = F.softmax(scaled_logits, dim=-1).cpu().numpy()
            
            # 获取top-5
            top_indices = np.argsort(probs)[::-1][:5]
            top_probs = probs[top_indices]
            
            # 计算熵
            entropy = -np.sum(probs * np.log(probs + 1e-10))
            max_entropy = np.log(len(probs))
            
            print(f"温度 {temp}:")
            print(f"  Top-5 indices: {top_indices}")
            print(f"  Top-5 probs: {top_probs}")
            print(f"  熵值: {entropy:.4f} / {max_entropy:.4f} ({entropy/max_entropy:.2%})")
            print()


def test_input_perturbation(model, tokenizer, sample_data, device, num_tests=10):
    """测试输入扰动的效果"""
    print("\n=== 测试输入扰动效果 ===")
    
    df, code_id = sample_data[0]
    original_tokens = tokenizer.tokenize(df)
    
    predictions = []
    
    model.eval()
    with torch.no_grad():
        for i in range(num_tests):
            # 创建扰动的输入
            if i == 0:
                # 原始输入
                tokens = original_tokens[-30:]
            else:
                # 随机选择不同的起始位置
                start_idx = max(0, len(original_tokens) - 30 - i)
                tokens = original_tokens[start_idx:start_idx+30]
                
                # 如果长度不够，用原始tokens填充
                if len(tokens) < 30:
                    tokens = original_tokens[-30:]
            
            input_tokens = torch.tensor(tokens, dtype=torch.int32).unsqueeze(0).to(device)
            code_ids = torch.tensor([code_id], dtype=torch.int32).to(device)
            
            # 对应的时间特征
            if i == 0:
                time_features = extract_time_features(df.iloc[-30:])
            else:
                start_idx = max(0, len(df) - 30 - i)
                time_features = extract_time_features(df.iloc[start_idx:start_idx+30])
                if len(time_features) < 30:
                    time_features = extract_time_features(df.iloc[-30:])
            
            time_features = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(device)
            
            # 前向传播
            logits, _ = model(input_tokens, code_ids, time_features)
            probs = F.softmax(logits[0, -1, :], dim=-1).cpu().numpy()
            
            # 获取top-1预测
            top1_idx = np.argmax(probs)
            top1_prob = probs[top1_idx]
            
            predictions.append((top1_idx, top1_prob))
            
            print(f"测试 {i}: Top-1 = {top1_idx} (概率: {top1_prob:.4f})")
    
    # 分析预测多样性
    unique_predictions = len(set(pred[0] for pred in predictions))
    print(f"\n预测多样性: {unique_predictions}/{num_tests} = {unique_predictions/num_tests:.2%}")


def test_different_samples(model, tokenizer, sample_data, device, num_samples=10):
    """测试不同样本的预测"""
    print(f"\n=== 测试不同样本预测 (测试{num_samples}个样本) ===")
    
    predictions = []
    
    model.eval()
    with torch.no_grad():
        for i in range(min(num_samples, len(sample_data))):
            df, code_id = sample_data[i]
            tokens = tokenizer.tokenize(df)
            
            if len(tokens) < 30:
                continue
            
            input_tokens = torch.tensor(tokens[-30:], dtype=torch.int32).unsqueeze(0).to(device)
            code_ids = torch.tensor([code_id], dtype=torch.int32).to(device)
            time_features = extract_time_features(df.iloc[-30:])
            time_features = torch.tensor(time_features, dtype=torch.float32).unsqueeze(0).to(device)
            
            # 前向传播
            logits, _ = model(input_tokens, code_ids, time_features)
            probs = F.softmax(logits[0, -1, :], dim=-1).cpu().numpy()
            
            # 获取top-5预测
            top_indices = np.argsort(probs)[::-1][:5]
            top_probs = probs[top_indices]
            
            predictions.append({
                'sample': i,
                'top_indices': top_indices,
                'top_probs': top_probs
            })
            
            print(f"样本 {i}: Top-5 = {top_indices} (概率: {top_probs})")
    
    # 分析预测多样性
    all_top1 = [pred['top_indices'][0] for pred in predictions]
    unique_top1 = len(set(all_top1))
    print(f"\nTop-1预测多样性: {unique_top1}/{len(predictions)} = {unique_top1/len(predictions):.2%}")


def suggest_improvements(model, tokenizer):
    """建议改进方案"""
    print("\n=== 改进建议 ===")
    
    print("1. 调整推理参数:")
    print("   - 增加温度参数 (temperature=1.5-2.0) 来增加预测随机性")
    print("   - 使用top-k采样 (top_k=10-50) 限制候选token")
    print("   - 使用nucleus采样 (top_p=0.8-0.95)")
    
    print("\n2. 检查训练数据:")
    print("   - 确保训练数据包含各种市场情况（上涨、下跌、震荡）")
    print("   - 检查数据标签分布是否均衡")
    print("   - 增加数据增强技术")
    
    print("\n3. 模型训练改进:")
    print("   - 增加训练轮数")
    print("   - 调整学习率调度")
    print("   - 使用更强的正则化（dropout, weight decay）")
    print("   - 尝试不同的损失函数（focal loss, label smoothing）")
    
    print("\n4. 模型架构调整:")
    print("   - 增加模型容量（更多层数或更大的隐藏维度）")
    print("   - 尝试不同的注意力机制")
    print("   - 添加残差连接或层归一化")
    
    print("\n5. 码本优化:")
    print("   - 重新训练VQ-VAE码本，确保码本向量多样性")
    print("   - 增加码本大小")
    print("   - 使用更好的聚类算法")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='修复CandlestickVQGPT模型预测多样性问题')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本文件路径')
    parser.add_argument('--data_path', type=str, required=True, help='数据文件路径')
    parser.add_argument('--vectorization_method', type=str, default='atr_based',
                        choices=['atr_based', 'percent_change', 'log_return', 'zscore', 'minmax'],
                        help='向量化方法')
    
    # 测试参数
    parser.add_argument('--num_samples', type=int, default=10, help='测试样本数量')
    parser.add_argument('--begin_date', type=str, default=None, help='开始日期')
    parser.add_argument('--end_date', type=str, default=None, help='结束日期')
    
    args = parser.parse_args()
    
    # 创建tokenizer
    print("创建Tokenizer...")
    tokenizer = create_tokenizer(args)
    print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")
    
    # 加载模型
    print("加载模型...")
    model, device = load_model(args, tokenizer)
    print(f"模型加载成功，设备: {device}")
    
    # 加载数据
    print(f"加载数据: {args.data_path}")
    data, code_ids = load_single_data(
        args.data_path,
        begin_date=args.begin_date,
        end_date=args.end_date
    )
    
    if len(data) == 0:
        print("错误: 未找到符合条件的数据")
        return
    
    # 准备数据样本
    sample_data = []
    for i, df in enumerate(data):
        if len(df) >= 50:  # 确保有足够的数据
            sample_data.append((df, code_ids[i]))
    
    print(f"有效数据样本: {len(sample_data)}")
    
    if len(sample_data) == 0:
        print("错误: 没有足够长的数据样本")
        return
    
    # 运行各种测试
    test_temperature_effects(model, tokenizer, sample_data, device)
    test_input_perturbation(model, tokenizer, sample_data, device)
    test_different_samples(model, tokenizer, sample_data, device, args.num_samples)
    
    # 提供改进建议
    suggest_improvements(model, tokenizer)


if __name__ == "__main__":
    main()
